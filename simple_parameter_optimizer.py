#!/usr/bin/env python3
"""
简化参数优化系统
不依赖外部库，使用网格搜索和随机搜索进行参数优化
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import json
import random
from datetime import datetime
import itertools

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from enhanced_multidimensional_predictor import EnhancedMultidimensionalPredictor


class SimpleParameterOptimizer:
    """简化参数优化器 - 使用网格搜索和随机搜索"""
    
    def __init__(self, data: List[Dict]):
        self.data = data
        self.best_params = None
        self.best_score = -np.inf
        self.optimization_history = []
        
        # 评估权重
        self.eval_weights = {
            'position_accuracy': 0.4,
            'zero_count_accuracy': 0.3,
            'perfect_match_rate': 0.2,
            'confidence_stability': 0.1
        }
        
    def define_parameter_ranges(self) -> Dict[str, List]:
        """定义参数搜索范围"""
        return {
            # 多维度权重 (会自动归一化)
            'zone_heat_weight': [0.15, 0.20, 0.25, 0.30, 0.35],
            'zero_slope_weight': [0.10, 0.15, 0.20],
            'position_zero_weight': [0.10, 0.15, 0.20],
            'missing_analysis_weight': [0.15, 0.20, 0.25],
            'adjacent_pattern_weight': [0.05, 0.10, 0.15],
            'hot_cold_balance_weight': [0.10, 0.15, 0.20],
            
            # 分析窗口
            'short_term_window': [8, 10, 12],
            'medium_term_window': [25, 30, 35],
            'long_term_window': [90, 100, 110],
            
            # 其他关键参数
            'exclude_recent_periods': [15, 20, 25],
            'enhancement_weight': [0.25, 0.30, 0.35],
        }
    
    def normalize_weights(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """归一化维度权重"""
        weight_keys = ['zone_heat_weight', 'zero_slope_weight', 'position_zero_weight', 
                      'missing_analysis_weight', 'adjacent_pattern_weight', 'hot_cold_balance_weight']
        
        total_weight = sum(params[key] for key in weight_keys)
        for key in weight_keys:
            params[key] = params[key] / total_weight
            
        return params
    
    def create_predictor_with_params(self, params: Dict[str, Any]) -> EnhancedMultidimensionalPredictor:
        """根据参数创建预测器"""
        predictor = EnhancedMultidimensionalPredictor(seed=42)
        
        # 更新维度权重
        predictor.dimension_weights = {
            'zone_heat': params['zone_heat_weight'],
            'zero_slope': params['zero_slope_weight'],
            'position_zero': params['position_zero_weight'],
            'missing_analysis': params['missing_analysis_weight'],
            'adjacent_pattern': params['adjacent_pattern_weight'],
            'hot_cold_balance': params['hot_cold_balance_weight']
        }
        
        # 更新分析窗口
        predictor.analysis_windows = {
            'short_term': params['short_term_window'],
            'medium_term': params['medium_term_window'],
            'long_term': params['long_term_window']
        }
        
        return predictor
    
    def evaluate_parameters(self, params: Dict[str, Any]) -> float:
        """评估参数组合性能"""
        try:
            predictor = self.create_predictor_with_params(params)
            
            # 使用最近30期进行测试，前200期作为训练
            test_periods = 15
            train_data = self.data[-(test_periods + 200):-test_periods]
            test_data = self.data[-test_periods:]
            
            total_matches = 0
            zero_count_matches = 0
            perfect_matches = 0
            confidence_scores = []
            total_positions = 0
            
            for i, test_record in enumerate(test_data):
                history = train_data + test_data[:i]
                recent_history = history[-50:]
                
                # 获取实际结果
                actual_zone_dist = self._parse_zone_ratio(test_record['分区比'])
                actual_pattern = self._get_zero_pattern(actual_zone_dist)
                
                # 生成预测
                predictions = predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)
                
                if not predictions:
                    continue
                
                # 评估第一个预测
                predicted_pattern, confidence, _ = predictions[0]
                confidence_scores.append(confidence)
                
                # 计算匹配度
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                total_matches += match_count
                total_positions += 7
                
                # 0分区数量匹配
                if predicted_pattern.count('0') == actual_pattern.count('0'):
                    zero_count_matches += 1
                
                # 检查完全匹配
                for pred_pattern, _, _ in predictions:
                    if pred_pattern == actual_pattern:
                        perfect_matches += 1
                        break
            
            if total_positions == 0:
                return -1000
            
            # 计算各项指标
            position_accuracy = total_matches / total_positions
            zero_count_accuracy = zero_count_matches / len(test_data) if test_data else 0
            perfect_match_rate = perfect_matches / len(test_data) if test_data else 0
            confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0
            
            # 综合评分
            score = (position_accuracy * self.eval_weights['position_accuracy'] +
                    zero_count_accuracy * self.eval_weights['zero_count_accuracy'] +
                    perfect_match_rate * self.eval_weights['perfect_match_rate'] +
                    confidence_stability * self.eval_weights['confidence_stability'])
            
            return score
            
        except Exception as e:
            print(f"参数评估出错: {e}")
            return -1000
    
    def random_search(self, n_trials: int = 50) -> Dict[str, Any]:
        """随机搜索优化"""
        print("=" * 80)
        print("随机搜索参数优化")
        print("=" * 80)
        print(f"试验次数: {n_trials}")
        
        param_ranges = self.define_parameter_ranges()
        
        for trial in range(n_trials):
            # 随机选择参数
            params = {}
            for param_name, param_range in param_ranges.items():
                params[param_name] = random.choice(param_range)
            
            # 归一化权重
            params = self.normalize_weights(params)
            
            # 评估参数
            score = self.evaluate_parameters(params)
            
            # 记录结果
            self.optimization_history.append({
                'trial': trial,
                'params': params.copy(),
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            # 更新最佳结果
            if score > self.best_score:
                self.best_score = score
                self.best_params = params.copy()
                print(f"Trial {trial}: 新的最佳分数 {score:.4f}")
                print(f"  最佳参数预览: zone_heat={params['zone_heat_weight']:.3f}, missing={params['missing_analysis_weight']:.3f}")
            
            if trial % 10 == 0:
                print(f"已完成 {trial}/{n_trials} 次试验")
        
        return self.best_params
    
    def grid_search_key_params(self) -> Dict[str, Any]:
        """对关键参数进行网格搜索"""
        print("=" * 80)
        print("关键参数网格搜索")
        print("=" * 80)
        
        # 只对最关键的参数进行网格搜索
        key_params = {
            'zone_heat_weight': [0.20, 0.25, 0.30],
            'missing_analysis_weight': [0.15, 0.20, 0.25],
            'short_term_window': [8, 10, 12],
            'medium_term_window': [25, 30, 35],
        }
        
        # 固定其他参数
        fixed_params = {
            'zero_slope_weight': 0.15,
            'position_zero_weight': 0.15,
            'adjacent_pattern_weight': 0.10,
            'hot_cold_balance_weight': 0.15,
            'long_term_window': 100,
            'exclude_recent_periods': 20,
            'enhancement_weight': 0.30,
        }
        
        # 生成所有参数组合
        param_names = list(key_params.keys())
        param_values = list(key_params.values())
        combinations = list(itertools.product(*param_values))
        
        print(f"总共 {len(combinations)} 种参数组合")
        
        for i, combination in enumerate(combinations):
            # 构建参数字典
            params = fixed_params.copy()
            for j, param_name in enumerate(param_names):
                params[param_name] = combination[j]
            
            # 归一化权重
            params = self.normalize_weights(params)
            
            # 评估参数
            score = self.evaluate_parameters(params)
            
            # 记录结果
            self.optimization_history.append({
                'trial': f'grid_{i}',
                'params': params.copy(),
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            # 更新最佳结果
            if score > self.best_score:
                self.best_score = score
                self.best_params = params.copy()
                print(f"Grid {i}: 新的最佳分数 {score:.4f}")
            
            if i % 5 == 0:
                print(f"已完成 {i}/{len(combinations)} 种组合")
        
        return self.best_params
    
    def _parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def _get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def save_results(self, filepath: str):
        """保存优化结果"""
        results = {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'optimization_history': self.optimization_history,
            'eval_weights': self.eval_weights,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"优化结果已保存到: {filepath}")


def main():
    """主函数"""
    try:
        # 加载数据
        print("加载历史数据...")
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')
        
        if len(data) < 300:
            print("数据不足，需要至少300期数据")
            return
        
        print(f"已加载 {len(data)} 期数据")
        
        # 创建优化器
        optimizer = SimpleParameterOptimizer(data)
        
        # 执行网格搜索
        print("\n开始参数优化...")
        best_params_grid = optimizer.grid_search_key_params()
        
        # 执行随机搜索
        best_params_random = optimizer.random_search(n_trials=30)
        
        # 输出最终结果
        print("\n" + "=" * 80)
        print("参数优化完成")
        print("=" * 80)
        print(f"最佳分数: {optimizer.best_score:.4f}")
        print(f"最佳参数:")
        for key, value in optimizer.best_params.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # 保存结果
        optimizer.save_results('simple_optimization_results.json')
        
        print("\n参数优化完成！建议使用最佳参数更新系统配置。")
        
    except Exception as e:
        print(f"优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
