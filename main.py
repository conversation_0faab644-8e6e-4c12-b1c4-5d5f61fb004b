"""
大乐透数据分析应用程序主入口 - 基于tkinter
"""
import sys
import os
import tkinter as tk

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from controller.main_controller import MainController
from view.main_window import MainWindow


def main():
    """主函数"""
    try:
        # 创建控制器
        controller = MainController()

        # 创建主窗口
        main_window = MainWindow(controller)

        # 运行应用程序
        main_window.run()

    except Exception as e:
        print(f"应用程序启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
