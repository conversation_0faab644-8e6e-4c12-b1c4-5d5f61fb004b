#!/usr/bin/env python3
"""
激进优化预测器
基于深度分析，实现更激进的优化策略
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class RadicalFeatureExtractor:
    """激进特征提取器 - 挖掘更深层的模式"""
    
    def __init__(self):
        pass
    
    def extract_comprehensive_features(self, data: List[Dict], window_size: int = 50) -> Dict[str, Any]:
        """提取综合特征"""
        if len(data) < window_size:
            return {}
        
        features = {}
        
        # 1. 基础统计特征
        zone_matrix = []
        zero_counts = []
        patterns = []
        
        for record in data[-window_size:]:
            zone_dist = self._parse_zone_ratio(record['分区比'])
            zone_matrix.append(zone_dist)
            zero_counts.append(sum(1 for x in zone_dist if x == 0))
            patterns.append(''.join(['0' if x == 0 else 'x' for x in zone_dist]))
        
        zone_matrix = np.array(zone_matrix)
        
        # 2. 高阶统计特征
        features['zone_means'] = np.mean(zone_matrix, axis=0)
        features['zone_stds'] = np.std(zone_matrix, axis=0)
        features['zone_skewness'] = self._calculate_skewness(zone_matrix)
        features['zone_kurtosis'] = self._calculate_kurtosis(zone_matrix)
        
        # 3. 时序相关性特征
        features['autocorrelations'] = self._calculate_autocorrelations(zero_counts)
        features['cross_correlations'] = self._calculate_cross_correlations(zone_matrix)
        
        # 4. 模式频率特征
        pattern_counts = Counter(patterns)
        features['pattern_diversity'] = len(pattern_counts)
        features['pattern_entropy'] = self._calculate_entropy(list(pattern_counts.values()))
        features['dominant_pattern_ratio'] = max(pattern_counts.values()) / len(patterns)
        
        # 5. 周期性特征
        features['periodicity_scores'] = self._detect_periodicity(zero_counts)
        
        # 6. 趋势特征
        features['trend_slopes'] = self._calculate_trend_slopes(zone_matrix)
        
        # 7. 波动性特征
        features['volatility_measures'] = self._calculate_volatility(zone_matrix)
        
        # 8. 分布特征
        features['distribution_features'] = self._analyze_distributions(zone_matrix)
        
        return features
    
    def _parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def _calculate_skewness(self, matrix: np.ndarray) -> List[float]:
        """计算偏度"""
        skewness = []
        for col in range(matrix.shape[1]):
            data = matrix[:, col]
            mean = np.mean(data)
            std = np.std(data)
            if std > 0:
                skew = np.mean(((data - mean) / std) ** 3)
            else:
                skew = 0
            skewness.append(skew)
        return skewness
    
    def _calculate_kurtosis(self, matrix: np.ndarray) -> List[float]:
        """计算峰度"""
        kurtosis = []
        for col in range(matrix.shape[1]):
            data = matrix[:, col]
            mean = np.mean(data)
            std = np.std(data)
            if std > 0:
                kurt = np.mean(((data - mean) / std) ** 4) - 3
            else:
                kurt = 0
            kurtosis.append(kurt)
        return kurtosis
    
    def _calculate_autocorrelations(self, series: List[int], max_lag: int = 10) -> List[float]:
        """计算自相关"""
        autocorrs = []
        series = np.array(series)
        n = len(series)
        
        for lag in range(1, min(max_lag + 1, n)):
            if n - lag > 0:
                corr = np.corrcoef(series[:-lag], series[lag:])[0, 1]
                autocorrs.append(corr if not np.isnan(corr) else 0)
            else:
                autocorrs.append(0)
        
        return autocorrs
    
    def _calculate_cross_correlations(self, matrix: np.ndarray) -> List[float]:
        """计算交叉相关"""
        cross_corrs = []
        n_zones = matrix.shape[1]
        
        for i in range(n_zones):
            for j in range(i + 1, n_zones):
                corr = np.corrcoef(matrix[:, i], matrix[:, j])[0, 1]
                cross_corrs.append(corr if not np.isnan(corr) else 0)
        
        return cross_corrs
    
    def _calculate_entropy(self, values: List[int]) -> float:
        """计算熵"""
        if not values:
            return 0
        
        total = sum(values)
        if total == 0:
            return 0
        
        probs = [v / total for v in values]
        entropy = -sum(p * np.log2(p) for p in probs if p > 0)
        return entropy
    
    def _detect_periodicity(self, series: List[int]) -> Dict[int, float]:
        """检测周期性"""
        periodicity = {}
        series = np.array(series)
        
        for period in [3, 5, 7, 10, 14]:
            if len(series) >= period * 2:
                correlations = []
                for offset in range(period):
                    if len(series) - offset >= period:
                        sub_series = series[offset::period]
                        if len(sub_series) > 1:
                            # 计算子序列的自相关
                            if len(sub_series) > 2:
                                corr = np.corrcoef(sub_series[:-1], sub_series[1:])[0, 1]
                                correlations.append(corr if not np.isnan(corr) else 0)
                
                periodicity[period] = np.mean(correlations) if correlations else 0
            else:
                periodicity[period] = 0
        
        return periodicity
    
    def _calculate_trend_slopes(self, matrix: np.ndarray) -> List[float]:
        """计算趋势斜率"""
        slopes = []
        x = np.arange(matrix.shape[0])
        
        for col in range(matrix.shape[1]):
            y = matrix[:, col]
            if len(y) > 1:
                slope = np.polyfit(x, y, 1)[0]
                slopes.append(slope)
            else:
                slopes.append(0)
        
        return slopes
    
    def _calculate_volatility(self, matrix: np.ndarray) -> Dict[str, List[float]]:
        """计算波动性指标"""
        volatility = {}
        
        # 滚动标准差
        window = min(10, matrix.shape[0] // 2)
        rolling_stds = []
        
        for col in range(matrix.shape[1]):
            col_data = matrix[:, col]
            stds = []
            for i in range(window, len(col_data)):
                window_data = col_data[i-window:i]
                stds.append(np.std(window_data))
            rolling_stds.append(np.mean(stds) if stds else 0)
        
        volatility['rolling_std'] = rolling_stds
        
        # 变化率
        change_rates = []
        for col in range(matrix.shape[1]):
            col_data = matrix[:, col]
            if len(col_data) > 1:
                changes = np.diff(col_data)
                change_rates.append(np.std(changes))
            else:
                change_rates.append(0)
        
        volatility['change_rate'] = change_rates
        
        return volatility
    
    def _analyze_distributions(self, matrix: np.ndarray) -> Dict[str, List[float]]:
        """分析分布特征"""
        distributions = {}
        
        # 分位数
        percentiles = [25, 50, 75, 90, 95]
        for p in percentiles:
            distributions[f'p{p}'] = [np.percentile(matrix[:, col], p) for col in range(matrix.shape[1])]
        
        # 极值比例
        distributions['zero_ratio'] = [np.mean(matrix[:, col] == 0) for col in range(matrix.shape[1])]
        distributions['max_ratio'] = [np.mean(matrix[:, col] == np.max(matrix[:, col])) for col in range(matrix.shape[1])]
        
        return distributions


class IntelligentPatternMatcher:
    """智能模式匹配器"""
    
    def __init__(self):
        self.pattern_database = defaultdict(list)
        self.feature_extractor = RadicalFeatureExtractor()
    
    def build_pattern_database(self, data: List[Dict]):
        """构建模式数据库"""
        print("构建智能模式数据库...")
        
        for i in range(50, len(data)):
            # 提取历史特征
            historical_features = self.feature_extractor.extract_comprehensive_features(data[:i])
            
            # 获取下一期的实际结果
            if i < len(data):
                next_record = data[i]
                zone_dist = self.feature_extractor._parse_zone_ratio(next_record['分区比'])
                actual_pattern = ''.join(['0' if x == 0 else 'x' for x in zone_dist])
                
                # 存储特征-模式对
                feature_signature = self._create_feature_signature(historical_features)
                self.pattern_database[feature_signature].append(actual_pattern)
        
        print(f"模式数据库构建完成，包含 {len(self.pattern_database)} 个特征签名")
    
    def _create_feature_signature(self, features: Dict[str, Any]) -> str:
        """创建特征签名"""
        if not features:
            return "default"
        
        signature_parts = []
        
        # 简化特征为离散值
        if 'zone_means' in features:
            means = features['zone_means']
            mean_signature = ''.join(['H' if x > np.mean(means) else 'L' for x in means])
            signature_parts.append(f"means:{mean_signature}")
        
        if 'pattern_diversity' in features:
            diversity = features['pattern_diversity']
            div_level = 'H' if diversity > 15 else 'M' if diversity > 10 else 'L'
            signature_parts.append(f"div:{div_level}")
        
        if 'dominant_pattern_ratio' in features:
            ratio = features['dominant_pattern_ratio']
            ratio_level = 'H' if ratio > 0.3 else 'M' if ratio > 0.15 else 'L'
            signature_parts.append(f"dom:{ratio_level}")
        
        return "|".join(signature_parts) if signature_parts else "default"
    
    def predict_patterns(self, data: List[Dict], n_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """基于模式匹配预测"""
        if not self.pattern_database:
            return [("xx0x0x0", 0.1, {})] * n_predictions
        
        # 提取当前特征
        current_features = self.feature_extractor.extract_comprehensive_features(data)
        current_signature = self._create_feature_signature(current_features)
        
        # 查找匹配的历史模式
        matching_patterns = self.pattern_database.get(current_signature, [])
        
        if not matching_patterns:
            # 寻找最相似的特征签名
            all_signatures = list(self.pattern_database.keys())
            similarities = [self._calculate_signature_similarity(current_signature, sig) for sig in all_signatures]
            
            if similarities:
                best_match_idx = np.argmax(similarities)
                best_signature = all_signatures[best_match_idx]
                matching_patterns = self.pattern_database[best_signature]
                similarity_score = similarities[best_match_idx]
            else:
                similarity_score = 0
        else:
            similarity_score = 1.0
        
        if not matching_patterns:
            return [("xx0x0x0", 0.1, {})] * n_predictions
        
        # 统计模式频率
        pattern_counts = Counter(matching_patterns)
        total_patterns = len(matching_patterns)
        
        # 生成预测
        predictions = []
        sorted_patterns = pattern_counts.most_common()
        
        for i, (pattern, count) in enumerate(sorted_patterns[:n_predictions]):
            confidence = (count / total_patterns) * similarity_score
            confidence = max(0.1, min(0.9, confidence + np.random.normal(0, 0.05)))
            
            info = {
                'pattern_matching': True,
                'historical_occurrences': count,
                'total_similar_cases': total_patterns,
                'feature_similarity': similarity_score,
                'signature': current_signature
            }
            
            predictions.append((pattern, confidence, info))
        
        # 如果预测不足，用随机生成补充
        while len(predictions) < n_predictions:
            random_pattern = self._generate_random_pattern()
            confidence = 0.1 + np.random.random() * 0.2
            info = {'pattern_matching': False, 'random_generation': True}
            predictions.append((random_pattern, confidence, info))
        
        return predictions[:n_predictions]
    
    def _calculate_signature_similarity(self, sig1: str, sig2: str) -> float:
        """计算特征签名相似度"""
        if sig1 == sig2:
            return 1.0
        
        parts1 = sig1.split('|')
        parts2 = sig2.split('|')
        
        common_parts = set(parts1) & set(parts2)
        total_parts = set(parts1) | set(parts2)
        
        if not total_parts:
            return 0.0
        
        return len(common_parts) / len(total_parts)
    
    def _generate_random_pattern(self) -> str:
        """生成随机模式"""
        # 生成合理的0分区数量（2-4个）
        zero_count = np.random.choice([2, 3, 4], p=[0.2, 0.5, 0.3])
        
        pattern = ['x'] * 7
        zero_positions = np.random.choice(7, size=zero_count, replace=False)
        
        for pos in zero_positions:
            pattern[pos] = '0'
        
        return ''.join(pattern)


class RadicalOptimizationPredictor:
    """激进优化预测器"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        np.random.seed(seed)
        self.pattern_matcher = IntelligentPatternMatcher()
        
    def train(self, data: List[Dict]):
        """训练模型"""
        self.pattern_matcher.build_pattern_database(data)
    
    def predict(self, data: List[Dict], n_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """激进预测"""
        return self.pattern_matcher.predict_patterns(data, n_predictions)


class RadicalOptimizationTester:
    """激进优化测试器"""

    def __init__(self):
        self.predictor = RadicalOptimizationPredictor(seed=42)

    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7

    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])

    def run_radical_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 400):
        """运行激进优化测试"""
        print("=" * 80)
        print("激进优化预测系统测试")
        print("=" * 80)

        # 准备数据
        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")

        # 训练模型
        print("\n开始训练激进优化模型...")
        self.predictor.train(train_data)

        print(f"\n预测结果:")
        print("-" * 80)

        # 统计变量
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        position_matches = [0] * 7
        confidence_scores = []
        pattern_matching_count = 0

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]

            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)

            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")

            # 生成激进预测
            predictions = self.predictor.predict(history, n_predictions=5)

            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')

                # 检查完全匹配
                if match_count == 7:
                    perfect_matches += 1
                    break

                if j == 0:  # 统计第一个预测
                    total_matches += match_count
                    confidence_scores.append(confidence)

                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1

                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1

                    # 统计模式匹配使用情况
                    if info.get('pattern_matching', False):
                        pattern_matching_count += 1

                # 显示预测信息
                method_info = "模式匹配" if info.get('pattern_matching', False) else "随机生成"
                detail_info = ""
                if info.get('pattern_matching', False):
                    occurrences = info.get('historical_occurrences', 0)
                    similarity = info.get('feature_similarity', 0)
                    detail_info = f"  历史:{occurrences}次  相似度:{similarity:.3f}"

                print(f"激进预测：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}  {method_info}{detail_info}")

            print()

        # 统计结果
        print(f"=" * 80)
        print("激进优化预测效果分析")
        print("=" * 80)

        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        perfect_accuracy = perfect_matches / test_periods
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0
        pattern_matching_ratio = pattern_matching_count / test_periods

        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        print(f"  完全匹配率(7/7): {perfect_accuracy:.1%} ({perfect_matches}/{test_periods}期)")
        print(f"  平均置信度: {avg_confidence:.3f}")
        print(f"  置信度稳定性: {confidence_stability:.3f}")
        print(f"  模式匹配使用率: {pattern_matching_ratio:.1%}")

        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")

        print(f"\n激进优化特性:")
        print(f"  智能模式数据库: {len(self.predictor.pattern_matcher.pattern_database)}个特征签名")
        print(f"  深度特征提取: 8个维度特征")
        print(f"  模式匹配算法: 基于特征相似度")

        # 计算综合评分
        radical_score = (avg_accuracy * 0.4 + zero_accuracy * 0.3 +
                        perfect_accuracy * 0.2 + pattern_matching_ratio * 0.1)
        print(f"\n激进优化评分: {radical_score:.4f}")

        return {
            'position_accuracy': avg_accuracy,
            'zero_count_accuracy': zero_accuracy,
            'perfect_match_rate': perfect_accuracy,
            'confidence': avg_confidence,
            'confidence_stability': confidence_stability,
            'pattern_matching_ratio': pattern_matching_ratio,
            'radical_score': radical_score
        }


def main():
    """主函数"""
    try:
        # 加载数据
        print("加载历史数据...")
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')

        if len(data) < 500:
            print("数据不足，需要至少500期数据")
            return

        print(f"已加载 {len(data)} 期数据")

        # 运行激进优化预测测试
        tester = RadicalOptimizationTester()
        results = tester.run_radical_test(data, test_periods=20, train_periods=400)

        print("\n激进优化预测系统测试完成！")
        print("\n与之前系统对比:")
        print("- 增强多维度系统: 54.3%位置准确率, 5%完全匹配率")
        print("- 高级集成系统: 53.6%位置准确率, 0%完全匹配率")
        print(f"- 激进优化系统: {results['position_accuracy']:.1%}位置准确率, {results['perfect_match_rate']:.1%}完全匹配率")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
