#!/usr/bin/env python3
"""
偏差校正预测系统
基于深度分析结果的针对性改进
"""
import sys
import os
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from typing import List, Dict, Tuple
import hashlib

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from segmented_markov_chain import SegmentedMarkovChain


class BiascorrectedPredictor:
    """偏差校正预测器 - 基于深度分析的针对性优化"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        self.markov = SegmentedMarkovChain(order=1)  # 使用1阶，分析显示效果最佳
        
        # 位置特定的校正系数（基于分析结果）
        self.position_bias_correction = {
            0: +0.131,  # 位置1: 预测偏低13.1%，需要增加0的概率
            1: -0.014,  # 位置2: 预测偏高1.4%，需要减少0的概率
            2: -0.035,  # 位置3: 预测偏高3.5%
            3: -0.021,  # 位置4: 预测偏高2.1%
            4: -0.113,  # 位置5: 预测偏高11.3%，最大偏差
            5: -0.058,  # 位置6: 预测偏高5.8%
            6: +0.111   # 位置7: 预测偏低11.1%，需要增加0的概率
        }
        
        # 改进的分段策略（基于分段准确率分析）
        # segment_2准确率最低(32.5%)，需要特别处理
        self.segment_weights = {
            'segment_1': 1.0,   # 55.0%准确率，权重正常
            'segment_2': 0.6,   # 32.5%准确率，降低权重
            'segment_3': 1.1    # 60.0%准确率，略微提高权重
        }
        
        # 0分区数量偏差校正（平均偏差+0.34，倾向于高估）
        self.zero_count_correction = -0.34

        # 排除最近期数设置
        self.exclude_recent_periods = 20  # 排除最近20期内出现过的模式
        
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])

    def get_recent_patterns_and_counts(self, recent_data: List[Dict], lookback_periods: int = None) -> Tuple[set, set]:
        """获取最近N期内出现过的0位置模式和0分区数量"""
        if lookback_periods is None:
            lookback_periods = self.exclude_recent_periods

        recent_patterns = set()
        recent_zero_counts = set()

        # 取最近N期数据
        recent_subset = recent_data[-lookback_periods:] if len(recent_data) >= lookback_periods else recent_data

        for record in recent_subset:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            zero_count = pattern.count('0')

            recent_patterns.add(pattern)
            recent_zero_counts.add(zero_count)

        return recent_patterns, recent_zero_counts
    
    def _get_context_hash(self, recent_data: List[Dict]) -> str:
        """基于最近数据生成上下文哈希"""
        if not recent_data:
            return "default"
        
        context_parts = []
        for record in recent_data[-5:]:
            context_parts.append(f"{record['期号']}:{record['分区比']}")
        
        context_str = "|".join(context_parts)
        return hashlib.md5(context_str.encode()).hexdigest()[:8]
    
    def predict_with_bias_correction(self, recent_data: List[Dict], num_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """使用偏差校正的预测"""
        if not recent_data:
            return [("xx0x0x0", 0.3, {})] * num_predictions

        # 获取最近20期内出现过的模式和数量
        recent_patterns, recent_zero_counts = self.get_recent_patterns_and_counts(recent_data)

        # 训练马尔科夫链
        self.markov.train_all_segments(recent_data)

        # 生成上下文哈希
        context_hash = self._get_context_hash(recent_data)

        predictions = []
        max_attempts = num_predictions * 20  # 增加最大尝试次数
        attempts = 0

        while len(predictions) < num_predictions and attempts < max_attempts:
            attempts += 1

            # 基础预测 - 增加随机种子确保多样性
            prediction_seed = int(hashlib.md5(f"{context_hash}_pred_{attempts}".encode()).hexdigest()[:8], 16)
            np.random.seed(prediction_seed)

            base_prediction, base_info = self.markov.predict_full_pattern(recent_data)
            base_confidence = base_info.get('overall_confidence', 0.3)

            # 应用位置偏差校正
            corrected_pattern = self._apply_position_bias_correction(
                base_prediction, context_hash, attempts
            )

            # 应用0分区数量校正
            corrected_pattern = self._apply_zero_count_correction(
                corrected_pattern, recent_data, context_hash, attempts
            )

            # 检查是否与最近20期内的模式重复
            if corrected_pattern in recent_patterns:
                continue  # 跳过重复的模式，尝试生成新的

            # 检查是否与已生成的预测重复
            existing_patterns = [p[0] for p in predictions]
            if corrected_pattern in existing_patterns:
                continue  # 跳过与已生成预测重复的模式

            # 检查0分区数量是否与最近20期重复
            predicted_zero_count = corrected_pattern.count('0')
            if predicted_zero_count in recent_zero_counts and len(recent_zero_counts) < 3:
                # 如果最近20期内的0分区数量种类少于3种，才排除重复数量
                continue

            # 应用分段权重调整
            adjusted_confidence = self._apply_segment_weight_adjustment(
                base_confidence, base_info.get('segment_confidences', {})
            )

            # 确保预测合理性
            final_pattern = self._ensure_pattern_validity(corrected_pattern)

            prediction_info = {
                'base_prediction': base_prediction,
                'corrected_pattern': corrected_pattern,
                'final_pattern': final_pattern,
                'base_confidence': base_confidence,
                'adjusted_confidence': adjusted_confidence,
                'corrections_applied': True,
                'exclude_recent_applied': True,
                'excluded_patterns_count': len(recent_patterns),
                'excluded_zero_counts': list(recent_zero_counts),
                'prediction_index': len(predictions)
            }

            predictions.append((final_pattern, adjusted_confidence, prediction_info))
        
        # 如果无法生成足够的非重复预测，用原始方法补充
        while len(predictions) < num_predictions:
            # 使用原始方法生成预测（不排除重复）
            base_prediction, base_info = self.markov.predict_full_pattern(recent_data)
            base_confidence = base_info.get('overall_confidence', 0.3)

            corrected_pattern = self._apply_position_bias_correction(
                base_prediction, context_hash, len(predictions) + 100
            )
            final_pattern = self._ensure_pattern_validity(corrected_pattern)

            fallback_info = {
                'base_prediction': base_prediction,
                'final_pattern': final_pattern,
                'base_confidence': base_confidence,
                'method': 'fallback_no_exclusion',
                'prediction_index': len(predictions)
            }

            predictions.append((final_pattern, base_confidence * 0.8, fallback_info))

        # 确保预测多样性
        predictions = self._ensure_corrected_diversity(predictions, context_hash)

        return predictions
    
    def _apply_position_bias_correction(self, pattern: str, context: str, index: int) -> str:
        """应用位置偏差校正"""
        pattern_list = list(pattern)
        
        # 生成确定性种子
        seed = int(hashlib.md5(f"{context}_pos_{index}".encode()).hexdigest()[:8], 16)
        np.random.seed(seed)
        
        for pos in range(7):
            correction = self.position_bias_correction[pos]
            
            # 如果校正值为正，增加该位置为0的概率
            if correction > 0 and pattern_list[pos] == 'x':
                # 基于校正强度决定是否改变
                if np.random.random() < abs(correction):
                    pattern_list[pos] = '0'
            
            # 如果校正值为负，减少该位置为0的概率
            elif correction < 0 and pattern_list[pos] == '0':
                if np.random.random() < abs(correction):
                    pattern_list[pos] = 'x'
        
        return ''.join(pattern_list)
    
    def _apply_zero_count_correction(self, pattern: str, recent_data: List[Dict], context: str, index: int) -> str:
        """应用0分区数量校正"""
        current_zero_count = pattern.count('0')
        
        # 计算目标0分区数量（考虑偏差校正）
        base_target = self.markov.predict_target_zero_count(recent_data)
        corrected_target = base_target + self.zero_count_correction
        
        # 四舍五入到整数，并确保在合理范围内
        target_zero_count = max(2, min(4, round(corrected_target)))
        
        if current_zero_count == target_zero_count:
            return pattern
        
        # 调整到目标数量
        return self._adjust_to_target_zeros(pattern, target_zero_count, context, index)
    
    def _adjust_to_target_zeros(self, pattern: str, target_zeros: int, context: str, index: int) -> str:
        """调整到目标0分区数量"""
        pattern_list = list(pattern)
        current_zeros = pattern_list.count('0')
        
        seed = int(hashlib.md5(f"{context}_zero_{index}".encode()).hexdigest()[:8], 16)
        
        if current_zeros < target_zeros:
            # 需要增加0分区
            needed = target_zeros - current_zeros
            x_positions = [i for i, char in enumerate(pattern_list) if char == 'x']
            
            # 优先选择偏差校正建议增加0的位置
            priority_positions = [pos for pos in x_positions if self.position_bias_correction[pos] > 0]
            other_positions = [pos for pos in x_positions if pos not in priority_positions]
            
            # 先从优先位置选择
            selected_positions = priority_positions[:needed]
            if len(selected_positions) < needed:
                remaining = needed - len(selected_positions)
                # 从其他位置确定性选择
                for i in range(min(remaining, len(other_positions))):
                    pos_index = (seed + i) % len(other_positions)
                    selected_positions.append(other_positions[pos_index])
            
            for pos in selected_positions:
                pattern_list[pos] = '0'
        
        elif current_zeros > target_zeros:
            # 需要减少0分区
            excess = current_zeros - target_zeros
            zero_positions = [i for i, char in enumerate(pattern_list) if char == '0']
            
            # 优先选择偏差校正建议减少0的位置
            priority_positions = [pos for pos in zero_positions if self.position_bias_correction[pos] < 0]
            other_positions = [pos for pos in zero_positions if pos not in priority_positions]
            
            # 先从优先位置选择
            selected_positions = priority_positions[:excess]
            if len(selected_positions) < excess:
                remaining = excess - len(selected_positions)
                for i in range(min(remaining, len(other_positions))):
                    pos_index = (seed + i) % len(other_positions)
                    selected_positions.append(other_positions[pos_index])
            
            for pos in selected_positions:
                pattern_list[pos] = 'x'
        
        return ''.join(pattern_list)
    
    def _apply_segment_weight_adjustment(self, base_confidence: float, segment_confidences: Dict) -> float:
        """应用分段权重调整"""
        if not segment_confidences:
            return base_confidence
        
        # 根据各分段的权重调整整体置信度
        weighted_confidence = 0
        total_weight = 0
        
        for segment_name, confidence in segment_confidences.items():
            weight = self.segment_weights.get(segment_name, 1.0)
            weighted_confidence += confidence * weight
            total_weight += weight
        
        if total_weight > 0:
            adjusted_confidence = weighted_confidence / total_weight
        else:
            adjusted_confidence = base_confidence
        
        return min(0.8, max(0.1, adjusted_confidence))
    
    def _ensure_pattern_validity(self, pattern: str) -> str:
        """确保模式有效性"""
        zero_count = pattern.count('0')
        
        # 确保在合理范围内
        if zero_count < 2:
            return self._force_min_zeros(pattern, 2)
        elif zero_count > 4:
            return self._force_max_zeros(pattern, 4)
        
        return pattern
    
    def _force_min_zeros(self, pattern: str, min_zeros: int) -> str:
        """强制最小0分区数量"""
        pattern_list = list(pattern)
        current_zeros = pattern_list.count('0')
        needed = min_zeros - current_zeros
        
        x_positions = [i for i, char in enumerate(pattern_list) if char == 'x']
        
        # 优先选择偏差校正建议的位置
        priority_positions = [pos for pos in x_positions if self.position_bias_correction[pos] > 0]
        
        selected = priority_positions[:needed]
        if len(selected) < needed:
            remaining_positions = [pos for pos in x_positions if pos not in selected]
            selected.extend(remaining_positions[:needed - len(selected)])
        
        for pos in selected:
            pattern_list[pos] = '0'
        
        return ''.join(pattern_list)
    
    def _force_max_zeros(self, pattern: str, max_zeros: int) -> str:
        """强制最大0分区数量"""
        pattern_list = list(pattern)
        current_zeros = pattern_list.count('0')
        excess = current_zeros - max_zeros
        
        zero_positions = [i for i, char in enumerate(pattern_list) if char == '0']
        
        # 优先选择偏差校正建议的位置
        priority_positions = [pos for pos in zero_positions if self.position_bias_correction[pos] < 0]
        
        selected = priority_positions[:excess]
        if len(selected) < excess:
            remaining_positions = [pos for pos in zero_positions if pos not in selected]
            selected.extend(remaining_positions[:excess - len(selected)])
        
        for pos in selected:
            pattern_list[pos] = 'x'
        
        return ''.join(pattern_list)
    
    def _ensure_corrected_diversity(self, predictions: List[Tuple[str, float, Dict]], context: str) -> List[Tuple[str, float, Dict]]:
        """确保校正后的预测多样性"""
        unique_predictions = []
        seen_patterns = set()
        
        for i, (pattern, confidence, info) in enumerate(predictions):
            if pattern not in seen_patterns:
                unique_predictions.append((pattern, confidence, info))
                seen_patterns.add(pattern)
            else:
                # 确定性修改重复模式
                modified_pattern = self._modify_pattern_with_correction(pattern, context, i)
                modified_info = info.copy()
                modified_info['diversity_modified'] = True
                unique_predictions.append((modified_pattern, confidence * 0.95, modified_info))
                seen_patterns.add(modified_pattern)
        
        return unique_predictions
    
    def _modify_pattern_with_correction(self, pattern: str, context: str, modifier: int) -> str:
        """使用偏差校正修改模式"""
        pattern_list = list(pattern)

        # 生成确定性种子，包含modifier确保不同修改
        seed = int(hashlib.md5(f"{context}_modify_{modifier}".encode()).hexdigest()[:8], 16)
        np.random.seed(seed)

        # 根据modifier选择不同的修改策略
        modification_strategies = [
            self._modify_by_max_bias,      # 策略1：修改偏差最大的位置
            self._modify_by_random_bias,   # 策略2：随机选择有偏差的位置
            self._modify_by_zero_count,    # 策略3：调整0分区数量
            self._modify_by_position_swap, # 策略4：交换相邻位置
            self._modify_by_segment        # 策略5：按分段修改
        ]

        strategy_index = modifier % len(modification_strategies)
        strategy = modification_strategies[strategy_index]

        result = strategy(pattern_list, modifier)

        # 确保修改后仍然合理
        if not (2 <= result.count('0') <= 4):
            return self._ensure_pattern_validity(result)
        
        return result

    def _modify_by_max_bias(self, pattern_list: List[str], modifier: int) -> str:
        """策略1：修改偏差最大的位置"""
        # 获取偏差最大的几个位置
        bias_positions = sorted(range(7), key=lambda x: abs(self.position_bias_correction[x]), reverse=True)
        # 根据modifier选择前几个偏差最大的位置之一
        max_bias_pos = bias_positions[modifier % min(3, len(bias_positions))]

        if pattern_list[max_bias_pos] == '0':
            pattern_list[max_bias_pos] = 'x'
        else:
            pattern_list[max_bias_pos] = '0'

        return ''.join(pattern_list)

    def _modify_by_random_bias(self, pattern_list: List[str], modifier: int) -> str:
        """策略2：随机选择有偏差的位置"""
        # 找到有显著偏差的位置（绝对值>0.05）
        significant_bias_positions = [pos for pos in range(7)
                                    if abs(self.position_bias_correction[pos]) > 0.05]

        if significant_bias_positions:
            pos = significant_bias_positions[modifier % len(significant_bias_positions)]
            if pattern_list[pos] == '0':
                pattern_list[pos] = 'x'
            else:
                pattern_list[pos] = '0'

        return ''.join(pattern_list)

    def _modify_by_zero_count(self, pattern_list: List[str], modifier: int) -> str:
        """策略3：调整0分区数量"""
        current_zeros = pattern_list.count('0')

        if modifier % 2 == 0:  # 偶数modifier增加0
            if current_zeros < 4:
                x_positions = [i for i, char in enumerate(pattern_list) if char == 'x']
                if x_positions:
                    pos = x_positions[modifier % len(x_positions)]
                    pattern_list[pos] = '0'
        else:  # 奇数modifier减少0
            if current_zeros > 2:
                zero_positions = [i for i, char in enumerate(pattern_list) if char == '0']
                if zero_positions:
                    pos = zero_positions[modifier % len(zero_positions)]
                    pattern_list[pos] = 'x'

        return ''.join(pattern_list)

    def _modify_by_position_swap(self, pattern_list: List[str], modifier: int) -> str:
        """策略4：交换相邻位置"""
        swap_pairs = [(0,1), (1,2), (2,3), (3,4), (4,5), (5,6)]
        pair_index = modifier % len(swap_pairs)
        pos1, pos2 = swap_pairs[pair_index]

        # 交换两个位置的值
        pattern_list[pos1], pattern_list[pos2] = pattern_list[pos2], pattern_list[pos1]

        return ''.join(pattern_list)

    def _modify_by_segment(self, pattern_list: List[str], modifier: int) -> str:
        """策略5：按分段修改"""
        segments = [[0,1], [2,3], [4,5,6]]
        segment_index = modifier % len(segments)
        segment = segments[segment_index]

        # 在选定分段内随机修改一个位置
        pos = segment[modifier % len(segment)]
        if pattern_list[pos] == '0':
            pattern_list[pos] = 'x'
        else:
            pattern_list[pos] = '0'

        return ''.join(pattern_list)


class BiasCorrectTester:
    """偏差校正测试器"""

    def __init__(self):
        self.predictor = BiascorrectedPredictor(seed=42)
        
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def run_bias_correction_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 200):
        """运行偏差校正测试"""
        print("=" * 80)
        print("偏差校正预测系统测试")
        print("=" * 80)

        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")
        print(f"校正机制: 位置偏差校正 + 0分区数量校正 + 分段权重调整")
        
        print(f"\n预测结果:")
        print("-" * 80)
        
        total_matches = 0
        zero_count_matches = 0
        position_matches = [0] * 7
        
        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-10:]
            
            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)
            
            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")
            
            predictions = self.predictor.predict_with_bias_correction(recent_history, num_predictions=5)
            
            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')

                if j == 0:  # 统计第一个预测
                    total_matches += match_count
                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1

                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1

                # 显示排除信息
                exclude_info = ""
                if info.get('exclude_recent_applied'):
                    excluded_count = info.get('excluded_patterns_count', 0)
                    exclude_info = f"  排除{excluded_count}个最近模式"
                elif info.get('method') == 'fallback_no_exclusion':
                    exclude_info = "  回退预测"

                print(f"偏差校正：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}{exclude_info}")
            
            print()
        
        # 统计结果
        print(f"=" * 80)
        print("偏差校正效果分析")
        print("=" * 80)
        
        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        
        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        
        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")
        
        print(f"\n校正效果:")
        print(f"  位置偏差校正: 针对位置1(+13.1%)和位置7(+11.1%)增强")
        print(f"  位置偏差校正: 针对位置5(-11.3%)减弱")
        print(f"  0分区数量校正: 平均减少0.34个")
        print(f"  分段权重调整: segment_2权重降至0.6")
        print(f"  排除机制: 排除最近{self.predictor.exclude_recent_periods}期内重复模式，提升预测新颖性")


def main():
    """主函数"""
    try:
        # 加载数据
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')
        
        if len(data) < 300:
            print("数据不足")
            return
        
        # 运行偏差校正测试
        tester = BiasCorrectTester()
        tester.run_bias_correction_test(data, test_periods=20, train_periods=200)
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
