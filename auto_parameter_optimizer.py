#!/usr/bin/env python3
"""
自动参数优化系统
基于o3-mini深度分析建议，实现智能参数搜索和优化
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
import json
import pickle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from enhanced_multidimensional_predictor import EnhancedMultidimensionalPredictor


class AutoParameterOptimizer:
    """自动参数优化器 - 基于贝叶斯优化寻找最优参数"""
    
    def __init__(self, data: List[Dict], optimization_target: str = 'comprehensive'):
        self.data = data
        self.optimization_target = optimization_target
        self.best_params = None
        self.best_score = -np.inf
        self.optimization_history = []
        
        # 优化目标配置
        self.target_weights = {
            'position_accuracy': 0.4,      # 位置准确率权重
            'zero_count_accuracy': 0.3,    # 0分区数量准确率权重
            'perfect_match_rate': 0.2,     # 完全匹配率权重
            'confidence_stability': 0.1    # 置信度稳定性权重
        }
        
    def define_search_space(self, trial: optuna.Trial) -> Dict[str, Any]:
        """定义参数搜索空间"""
        params = {
            # 多维度权重优化
            'zone_heat_weight': trial.suggest_float('zone_heat_weight', 0.1, 0.4),
            'zero_slope_weight': trial.suggest_float('zero_slope_weight', 0.05, 0.25),
            'position_zero_weight': trial.suggest_float('position_zero_weight', 0.05, 0.25),
            'missing_analysis_weight': trial.suggest_float('missing_analysis_weight', 0.1, 0.3),
            'adjacent_pattern_weight': trial.suggest_float('adjacent_pattern_weight', 0.05, 0.2),
            'hot_cold_balance_weight': trial.suggest_float('hot_cold_balance_weight', 0.05, 0.25),
            
            # 分析窗口优化
            'short_term_window': trial.suggest_int('short_term_window', 5, 15),
            'medium_term_window': trial.suggest_int('medium_term_window', 20, 40),
            'long_term_window': trial.suggest_int('long_term_window', 80, 120),
            
            # 马尔科夫链参数
            'markov_order': trial.suggest_int('markov_order', 1, 3),
            'exclude_recent_periods': trial.suggest_int('exclude_recent_periods', 10, 30),
            
            # 预测融合参数
            'base_prediction_weight': trial.suggest_float('base_prediction_weight', 0.6, 0.8),
            'enhancement_weight': trial.suggest_float('enhancement_weight', 0.2, 0.4),
            
            # 0分区预测参数
            'zero_count_base_weight': trial.suggest_float('zero_count_base_weight', 0.7, 0.9),
            'zero_count_trend_weight': trial.suggest_float('zero_count_trend_weight', 0.05, 0.15),
            'diversity_bonus': trial.suggest_float('diversity_bonus', 0.1, 0.3),
            
            # 位置偏差校正参数
            'position_bias_strength': trial.suggest_float('position_bias_strength', 0.05, 0.2),
            'zero_count_correction_strength': trial.suggest_float('zero_count_correction_strength', 0.1, 0.5),
        }
        
        # 确保权重归一化
        total_weight = (params['zone_heat_weight'] + params['zero_slope_weight'] + 
                       params['position_zero_weight'] + params['missing_analysis_weight'] + 
                       params['adjacent_pattern_weight'] + params['hot_cold_balance_weight'])
        
        for key in ['zone_heat_weight', 'zero_slope_weight', 'position_zero_weight', 
                   'missing_analysis_weight', 'adjacent_pattern_weight', 'hot_cold_balance_weight']:
            params[key] = params[key] / total_weight
            
        return params
    
    def create_predictor_with_params(self, params: Dict[str, Any]) -> EnhancedMultidimensionalPredictor:
        """根据参数创建预测器"""
        predictor = EnhancedMultidimensionalPredictor(seed=42)
        
        # 更新维度权重
        predictor.dimension_weights = {
            'zone_heat': params['zone_heat_weight'],
            'zero_slope': params['zero_slope_weight'],
            'position_zero': params['position_zero_weight'],
            'missing_analysis': params['missing_analysis_weight'],
            'adjacent_pattern': params['adjacent_pattern_weight'],
            'hot_cold_balance': params['hot_cold_balance_weight']
        }
        
        # 更新分析窗口
        predictor.analysis_windows = {
            'short_term': params['short_term_window'],
            'medium_term': params['medium_term_window'],
            'long_term': params['long_term_window']
        }
        
        # 更新基础预测器参数
        if hasattr(predictor.base_predictor, 'exclude_recent_periods'):
            predictor.base_predictor.exclude_recent_periods = params['exclude_recent_periods']
        
        return predictor
    
    def evaluate_parameters(self, params: Dict[str, Any], test_periods: int = 15) -> float:
        """评估参数组合的性能"""
        try:
            # 创建预测器
            predictor = self.create_predictor_with_params(params)
            
            # 时间序列交叉验证
            total_score = 0
            n_folds = 3
            
            for fold in range(n_folds):
                # 计算训练和测试数据范围
                test_start = len(self.data) - (n_folds - fold) * test_periods
                test_end = test_start + test_periods
                train_end = test_start
                train_start = max(0, train_end - 200)  # 使用200期训练数据
                
                if test_end > len(self.data) or train_start >= train_end:
                    continue
                
                train_data = self.data[train_start:train_end]
                test_data = self.data[test_start:test_end]
                
                # 评估预测性能
                fold_score = self._evaluate_fold(predictor, train_data, test_data)
                total_score += fold_score
            
            return total_score / n_folds if n_folds > 0 else 0
            
        except Exception as e:
            print(f"参数评估出错: {e}")
            return -1000  # 返回很低的分数表示失败
    
    def _evaluate_fold(self, predictor, train_data: List[Dict], test_data: List[Dict]) -> float:
        """评估单个折叠的性能"""
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        confidence_scores = []
        total_positions = 0
        
        for i, test_record in enumerate(test_data):
            try:
                # 构建历史数据
                history = train_data + test_data[:i]
                recent_history = history[-50:]
                
                # 获取实际结果
                actual_zone_dist = self._parse_zone_ratio(test_record['分区比'])
                actual_pattern = self._get_zero_pattern(actual_zone_dist)
                
                # 生成预测
                predictions = predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)
                
                if not predictions:
                    continue
                
                # 评估第一个预测
                predicted_pattern, confidence, _ = predictions[0]
                confidence_scores.append(confidence)
                
                # 计算匹配度
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                total_matches += match_count
                total_positions += 7
                
                # 0分区数量匹配
                if predicted_pattern.count('0') == actual_pattern.count('0'):
                    zero_count_matches += 1
                
                # 检查是否有完全匹配
                for pred_pattern, _, _ in predictions:
                    if pred_pattern == actual_pattern:
                        perfect_matches += 1
                        break
                        
            except Exception as e:
                continue
        
        if total_positions == 0:
            return -1000
        
        # 计算各项指标
        position_accuracy = total_matches / total_positions
        zero_count_accuracy = zero_count_matches / len(test_data) if test_data else 0
        perfect_match_rate = perfect_matches / len(test_data) if test_data else 0
        confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0
        
        # 综合评分
        score = (position_accuracy * self.target_weights['position_accuracy'] +
                zero_count_accuracy * self.target_weights['zero_count_accuracy'] +
                perfect_match_rate * self.target_weights['perfect_match_rate'] +
                confidence_stability * self.target_weights['confidence_stability'])
        
        return score
    
    def _parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def _get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def optimize(self, n_trials: int = 100, timeout: int = 3600) -> Dict[str, Any]:
        """执行参数优化"""
        print("=" * 80)
        print("自动参数优化系统启动")
        print("=" * 80)
        print(f"优化目标: {self.optimization_target}")
        print(f"试验次数: {n_trials}")
        print(f"超时时间: {timeout}秒")
        print(f"数据量: {len(self.data)}期")
        
        def objective(trial):
            params = self.define_search_space(trial)
            score = self.evaluate_parameters(params)
            
            # 记录优化历史
            self.optimization_history.append({
                'trial': trial.number,
                'params': params,
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            if score > self.best_score:
                self.best_score = score
                self.best_params = params.copy()
                print(f"Trial {trial.number}: 新的最佳分数 {score:.4f}")
            
            return score
        
        # 创建优化研究
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner()
        )
        
        # 执行优化
        study.optimize(objective, n_trials=n_trials, timeout=timeout)
        
        # 输出结果
        print("\n" + "=" * 80)
        print("参数优化完成")
        print("=" * 80)
        print(f"最佳分数: {study.best_value:.4f}")
        print(f"最佳参数:")
        for key, value in study.best_params.items():
            print(f"  {key}: {value:.4f}")
        
        return study.best_params
    
    def save_optimization_results(self, filepath: str):
        """保存优化结果"""
        results = {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'optimization_history': self.optimization_history,
            'target_weights': self.target_weights,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"优化结果已保存到: {filepath}")


def main():
    """主函数"""
    try:
        # 加载数据
        print("加载历史数据...")
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')
        
        if len(data) < 300:
            print("数据不足，需要至少300期数据")
            return
        
        print(f"已加载 {len(data)} 期数据")
        
        # 创建优化器
        optimizer = AutoParameterOptimizer(data, optimization_target='comprehensive')
        
        # 执行优化
        best_params = optimizer.optimize(n_trials=50, timeout=1800)  # 30分钟超时
        
        # 保存结果
        optimizer.save_optimization_results('optimization_results.json')
        
        print("\n自动参数优化完成！")

        # 使用最优参数进行详细测试
        print("\n" + "=" * 80)
        print("使用最优参数进行详细测试")
        print("=" * 80)

        # 创建最优预测器
        optimal_predictor = optimizer.create_predictor_with_params(best_params)

        # 运行详细测试
        detailed_tester = DetailedPerformanceEvaluator(optimal_predictor, data)
        detailed_results = detailed_tester.comprehensive_evaluation(test_periods=20)

        print("详细测试结果:")
        for metric, value in detailed_results.items():
            if isinstance(value, float):
                print(f"  {metric}: {value:.4f}")
            else:
                print(f"  {metric}: {value}")

    except Exception as e:
        print(f"优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


class DetailedPerformanceEvaluator:
    """详细性能评估器 - 实现o3-mini建议的多维度评价体系"""

    def __init__(self, predictor, data: List[Dict]):
        self.predictor = predictor
        self.data = data

    def comprehensive_evaluation(self, test_periods: int = 20) -> Dict[str, Any]:
        """综合评估预测器性能"""
        # 准备测试数据
        train_data = self.data[-(test_periods + 200):-test_periods]
        test_data = self.data[-test_periods:]

        results = {}

        # 基础准确率指标
        basic_metrics = self._calculate_basic_metrics(train_data, test_data)
        results.update(basic_metrics)

        # 概率分布指标
        prob_metrics = self._calculate_probability_metrics(train_data, test_data)
        results.update(prob_metrics)

        # 稳定性指标
        stability_metrics = self._calculate_stability_metrics(train_data, test_data)
        results.update(stability_metrics)

        # 分段性能指标
        segment_metrics = self._calculate_segment_metrics(train_data, test_data)
        results.update(segment_metrics)

        return results

    def _calculate_basic_metrics(self, train_data: List[Dict], test_data: List[Dict]) -> Dict[str, float]:
        """计算基础准确率指标"""
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        position_matches = [0] * 7
        total_periods = len(test_data)

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]

            actual_zone_dist = self._parse_zone_ratio(test_record['分区比'])
            actual_pattern = self._get_zero_pattern(actual_zone_dist)

            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)

            if predictions:
                # 第一个预测的性能
                predicted_pattern, _, _ = predictions[0]
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                total_matches += match_count

                # 位置匹配统计
                for pos in range(7):
                    if predicted_pattern[pos] == actual_pattern[pos]:
                        position_matches[pos] += 1

                # 0分区数量匹配
                if predicted_pattern.count('0') == actual_pattern.count('0'):
                    zero_count_matches += 1

                # 完全匹配检查
                for pred_pattern, _, _ in predictions:
                    if pred_pattern == actual_pattern:
                        perfect_matches += 1
                        break

        return {
            'position_accuracy': total_matches / (total_periods * 7),
            'zero_count_accuracy': zero_count_matches / total_periods,
            'perfect_match_rate': perfect_matches / total_periods,
            'position_1_accuracy': position_matches[0] / total_periods,
            'position_2_accuracy': position_matches[1] / total_periods,
            'position_3_accuracy': position_matches[2] / total_periods,
            'position_4_accuracy': position_matches[3] / total_periods,
            'position_5_accuracy': position_matches[4] / total_periods,
            'position_6_accuracy': position_matches[5] / total_periods,
            'position_7_accuracy': position_matches[6] / total_periods,
        }

    def _calculate_probability_metrics(self, train_data: List[Dict], test_data: List[Dict]) -> Dict[str, float]:
        """计算概率分布相关指标"""
        confidence_scores = []
        prediction_entropies = []

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]

            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)

            if predictions:
                # 置信度统计
                confidences = [conf for _, conf, _ in predictions]
                confidence_scores.extend(confidences)

                # 预测熵计算
                if len(confidences) > 1:
                    # 归一化置信度作为概率分布
                    probs = np.array(confidences)
                    probs = probs / np.sum(probs)
                    entropy = -np.sum(probs * np.log(probs + 1e-10))
                    prediction_entropies.append(entropy)

        return {
            'mean_confidence': np.mean(confidence_scores) if confidence_scores else 0,
            'confidence_std': np.std(confidence_scores) if confidence_scores else 0,
            'mean_prediction_entropy': np.mean(prediction_entropies) if prediction_entropies else 0,
        }

    def _calculate_stability_metrics(self, train_data: List[Dict], test_data: List[Dict]) -> Dict[str, float]:
        """计算稳定性指标"""
        daily_accuracies = []

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]

            actual_zone_dist = self._parse_zone_ratio(test_record['分区比'])
            actual_pattern = self._get_zero_pattern(actual_zone_dist)

            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=1)

            if predictions:
                predicted_pattern, _, _ = predictions[0]
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                daily_accuracy = match_count / 7
                daily_accuracies.append(daily_accuracy)

        return {
            'accuracy_stability': 1 - np.std(daily_accuracies) if daily_accuracies else 0,
            'min_daily_accuracy': np.min(daily_accuracies) if daily_accuracies else 0,
            'max_daily_accuracy': np.max(daily_accuracies) if daily_accuracies else 0,
        }

    def _calculate_segment_metrics(self, train_data: List[Dict], test_data: List[Dict]) -> Dict[str, float]:
        """计算分段性能指标"""
        # 定义分段
        segments = {
            'segment_1': [0, 1, 2],  # 位置1-3
            'segment_2': [3, 4],     # 位置4-5
            'segment_3': [5, 6]      # 位置6-7
        }

        segment_accuracies = {name: [] for name in segments.keys()}

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]

            actual_zone_dist = self._parse_zone_ratio(test_record['分区比'])
            actual_pattern = self._get_zero_pattern(actual_zone_dist)

            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=1)

            if predictions:
                predicted_pattern, _, _ = predictions[0]

                for segment_name, positions in segments.items():
                    segment_matches = sum(1 for pos in positions
                                        if predicted_pattern[pos] == actual_pattern[pos])
                    segment_accuracy = segment_matches / len(positions)
                    segment_accuracies[segment_name].append(segment_accuracy)

        results = {}
        for segment_name, accuracies in segment_accuracies.items():
            if accuracies:
                results[f'{segment_name}_accuracy'] = np.mean(accuracies)

        return results

    def _parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7

    def _get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])


if __name__ == "__main__":
    main()
