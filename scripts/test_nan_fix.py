#!/usr/bin/env python3
"""
测试NaN修复功能
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.lottery_config import LotteryType, LotteryConfig
from model.data_model import LotteryDataModel
from analysis.analyzer import LotteryAnalyzer


def create_test_data_with_nan():
    """创建包含NaN的测试数据"""
    print("创建包含NaN的测试数据...")
    
    test_data = """期号,开奖日期,红球1,红球2,红球3,红球4,红球5,红球6,蓝球1
2023001,2023-01-01,03,11,16,22,27,32,05
2023002,2023-01-03,01,07,14,19,25,,12
2023003,2023-01-05,05,09,18,23,28,33,
2023004,2023-01-08,02,12,17,21,26,30,15
2023005,2023-01-10,,10,15,20,24,29,03"""
    
    test_file = "data/test_ssq_with_nan.csv"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_data)
    
    print(f"测试数据已保存到: {test_file}")
    return test_file


def test_nan_handling():
    """测试NaN处理"""
    print("=" * 60)
    print("测试NaN处理功能")
    print("=" * 60)
    
    # 创建测试数据
    test_file = create_test_data_with_nan()
    
    try:
        # 测试数据加载
        data_model = LotteryDataModel()
        success = data_model.load_csv_data(test_file, LotteryType.SSQ)
        
        if success:
            print(f"✓ 数据加载成功，有效记录数: {data_model.get_data_count()}")
            
            # 测试数据提取
            for i in range(min(3, data_model.get_data_count())):
                red_balls = data_model.get_red_balls(i)
                blue_balls = data_model.get_blue_balls(i)
                print(f"  记录{i+1}: 红球{red_balls}, 蓝球{blue_balls}")
            
            # 测试分析
            analyzer = LotteryAnalyzer()
            raw_data = data_model.get_raw_data()
            analyzed_data = analyzer.analyze_data(raw_data, LotteryType.SSQ)
            
            if analyzed_data is not None and len(analyzed_data) > 0:
                print(f"✓ 分析成功，分析结果: {len(analyzed_data)} 条记录")
                
                # 显示前几条分析结果
                print("\n分析结果示例:")
                for i in range(min(2, len(analyzed_data))):
                    row = analyzed_data.iloc[i]
                    print(f"  {i+1}. 日期:{row['日期']} 红球:{row['红球']} "
                          f"奇偶比:{row['奇偶比']} 分区比:{row['分区比']}")
            else:
                print("✗ 分析失败")
        else:
            print("✗ 数据加载失败")
    
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n已清理测试文件: {test_file}")


def test_real_ssq_data():
    """测试真实双色球数据"""
    print("\n" + "=" * 60)
    print("测试真实双色球数据")
    print("=" * 60)
    
    ssq_file = "data/ssq_data.csv"
    
    if not os.path.exists(ssq_file):
        print(f"✗ 双色球数据文件不存在: {ssq_file}")
        return
    
    try:
        # 检查数据文件内容
        print(f"检查数据文件: {ssq_file}")
        df = pd.read_csv(ssq_file)
        print(f"原始数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否有NaN
        nan_count = df.isnull().sum().sum()
        if nan_count > 0:
            print(f"发现 {nan_count} 个空值")
            print("空值分布:")
            for col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    print(f"  {col}: {null_count} 个空值")
        else:
            print("✓ 数据文件没有空值")
        
        # 测试加载
        data_model = LotteryDataModel()
        success = data_model.load_csv_data(ssq_file, LotteryType.SSQ)
        
        if success:
            print(f"✓ 双色球数据加载成功: {data_model.get_data_count()} 条记录")
            
            # 测试分析
            analyzer = LotteryAnalyzer()
            raw_data = data_model.get_raw_data()
            
            # 只分析前5条数据以节省时间
            test_data = raw_data.head(5)
            analyzed_data = analyzer.analyze_data(test_data, LotteryType.SSQ)
            
            if analyzed_data is not None and len(analyzed_data) > 0:
                print(f"✓ 双色球分析成功: {len(analyzed_data)} 条记录")
                
                print("\n分析结果:")
                for i in range(len(analyzed_data)):
                    row = analyzed_data.iloc[i]
                    print(f"  {i+1}. 期号:{row['期号']} 红球:{row['红球']} 蓝球:{row['蓝球']} "
                          f"奇偶比:{row['奇偶比']} 分区比:{row['分区比']}")
            else:
                print("✗ 双色球分析失败")
        else:
            print("✗ 双色球数据加载失败")
    
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("NaN处理功能测试")
    
    # 测试NaN处理
    test_nan_handling()
    
    # 测试真实数据
    test_real_ssq_data()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("现在可以尝试在GUI中导入双色球数据")


if __name__ == "__main__":
    main()
