#!/usr/bin/env python3
"""
快速启动脚本 - 检查依赖并运行应用程序
"""
import sys
import subprocess
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "config/requirements.txt"])
        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False


def check_data_file():
    """检查数据文件是否存在"""
    if os.path.exists("data/dlt_data.csv"):
        print("发现数据文件: data/dlt_data.csv")
        return True
    else:
        print("提示: 未发现默认数据文件 data/dlt_data.csv")
        print("您可以通过应用程序的'导入数据'按钮加载CSV文件")
        return True


def run_application():
    """运行应用程序"""
    print("启动大乐透数据分析系统...")
    try:
        # 切换到项目根目录
        os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # 导入主模块
        import main
        main.main()
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有依赖包已正确安装")
        return False
    except Exception as e:
        print(f"应用程序运行失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("大乐透数据分析系统启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查并安装依赖
    if not install_requirements():
        return
    
    # 检查数据文件
    check_data_file()
    
    print("\n" + "=" * 50)
    
    # 运行应用程序
    run_application()


if __name__ == "__main__":
    main()
