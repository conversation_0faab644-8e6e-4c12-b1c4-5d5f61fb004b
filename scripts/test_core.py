#!/usr/bin/env python3
"""
核心功能测试脚本 - 测试数据分析功能（不依赖GUI）
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model.data_model import LotteryDataModel
from analysis.analyzer import LotteryAnalyzer


def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    model = LotteryDataModel()
    
    # 测试加载现有数据文件
    data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "dlt_data.csv")
    if os.path.exists(data_file):
        success = model.load_csv_data(data_file)
        if success:
            print(f"✓ 数据加载成功，共 {model.get_data_count()} 条记录")
            
            # 显示前5条数据
            data = model.get_raw_data()
            print("\n前5条原始数据:")
            print(data.head())
            
            return model
        else:
            print("✗ 数据加载失败")
            return None
    else:
        print("✗ 数据文件 data/dlt_data.csv 不存在")
        return None


def test_analysis(model):
    """测试数据分析功能"""
    print("\n" + "=" * 50)
    print("测试数据分析功能")
    print("=" * 50)
    
    if model is None:
        print("✗ 无法进行分析测试，数据模型为空")
        return
    
    analyzer = LotteryAnalyzer()
    raw_data = model.get_raw_data()
    
    if raw_data is None or len(raw_data) == 0:
        print("✗ 无法进行分析测试，原始数据为空")
        return
    
    # 进行数据分析
    print("正在分析数据...")
    analyzed_data = analyzer.analyze_data(raw_data)
    
    if analyzed_data is not None and len(analyzed_data) > 0:
        print(f"✓ 数据分析成功，生成 {len(analyzed_data)} 条分析结果")
        
        # 显示前5条分析结果
        print("\n前5条分析结果:")
        print(analyzed_data.head())
        
        # 显示列信息
        print(f"\n分析结果包含以下列:")
        for i, col in enumerate(analyzed_data.columns, 1):
            print(f"{i:2d}. {col}")
        
        return analyzed_data
    else:
        print("✗ 数据分析失败")
        return None


def test_specific_analysis():
    """测试特定分析功能"""
    print("\n" + "=" * 50)
    print("测试特定分析功能")
    print("=" * 50)
    
    # 测试奇偶分析
    from analysis.odd_even_analysis import calculate_odd_even_ratio, calculate_odd_even_pattern
    
    test_red_balls = [1, 8, 15, 22, 29]  # 3奇2偶
    ratio = calculate_odd_even_ratio(test_red_balls)
    pattern = calculate_odd_even_pattern(test_red_balls)
    
    print(f"测试红球: {test_red_balls}")
    print(f"奇偶比: {ratio}")
    print(f"奇偶排布: {pattern}")
    
    # 测试分区分析
    from analysis.zone_analysis import calculate_zone_distribution, format_zone_ratio
    
    zones = calculate_zone_distribution(test_red_balls)
    zone_ratio = format_zone_ratio(zones)
    
    print(f"分区分布: {zones}")
    print(f"分区比: {zone_ratio}")
    
    print("✓ 特定分析功能测试完成")


def main():
    """主函数"""
    print("大乐透数据分析系统 - 核心功能测试")
    
    try:
        # 测试数据加载
        model = test_data_loading()
        
        # 测试数据分析
        analyzed_data = test_analysis(model)
        
        # 测试特定分析功能
        test_specific_analysis()
        
        print("\n" + "=" * 50)
        print("核心功能测试完成")
        print("=" * 50)
        
        if analyzed_data is not None:
            print("✓ 所有核心功能正常工作")
            print("您可以运行 python main.py 启动GUI应用程序")
        else:
            print("⚠ 部分功能可能存在问题")
            
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
