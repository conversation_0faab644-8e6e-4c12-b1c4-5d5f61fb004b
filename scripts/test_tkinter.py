#!/usr/bin/env python3
"""
tkinter GUI测试脚本
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_tkinter_import():
    """测试tkinter导入"""
    print("=" * 50)
    print("测试tkinter导入")
    print("=" * 50)
    
    try:
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox
        print("✓ tkinter 导入成功")
        print(f"✓ tkinter版本: {tk.TkVersion}")
        return True
    except ImportError as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False


def test_basic_window():
    """测试基本窗口创建"""
    print("\n" + "=" * 50)
    print("测试基本窗口创建")
    print("=" * 50)
    
    try:
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加一些基本组件
        label = ttk.Label(root, text="这是一个测试窗口")
        label.pack(pady=20)
        
        button = ttk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✓ 基本窗口创建成功")
        print("窗口将在3秒后自动关闭...")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 窗口创建失败: {e}")
        return False


def test_treeview_table():
    """测试Treeview表格"""
    print("\n" + "=" * 50)
    print("测试Treeview表格")
    print("=" * 50)
    
    try:
        root = tk.Tk()
        root.title("表格测试")
        root.geometry("800x400")
        
        # 创建表格
        columns = ['列1', '列2', '列3']
        tree = ttk.Treeview(root, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)
        
        # 添加测试数据
        for i in range(5):
            tree.insert('', 'end', values=[f'数据{i+1}-1', f'数据{i+1}-2', f'数据{i+1}-3'])
        
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加关闭按钮
        close_btn = ttk.Button(root, text="关闭", command=root.destroy)
        close_btn.pack(pady=5)
        
        print("✓ Treeview表格创建成功")
        print("窗口将在5秒后自动关闭...")
        
        # 5秒后自动关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 表格创建失败: {e}")
        return False


def test_file_dialog():
    """测试文件对话框"""
    print("\n" + "=" * 50)
    print("测试文件对话框")
    print("=" * 50)
    
    try:
        from tkinter import filedialog
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("✓ 文件对话框模块导入成功")
        print("注意: 实际使用时会弹出文件选择对话框")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 文件对话框测试失败: {e}")
        return False


def test_core_functionality():
    """测试核心功能"""
    print("\n" + "=" * 50)
    print("测试核心分析功能")
    print("=" * 50)
    
    try:
        from model.data_model import LotteryDataModel
        from analysis.analyzer import LotteryAnalyzer
        
        print("✓ 核心模块导入成功")
        
        # 测试数据模型
        model = LotteryDataModel()
        print("✓ 数据模型创建成功")
        
        # 测试分析器
        analyzer = LotteryAnalyzer()
        print("✓ 分析器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("tkinter GUI 测试程序")
    print("适用于Python 3.13")
    
    # 测试tkinter导入
    if not test_tkinter_import():
        print("\ntkinter不可用，无法继续测试")
        return
    
    # 测试核心功能
    if not test_core_functionality():
        print("\n核心功能有问题，建议先解决")
        return
    
    print("\n选择测试项目:")
    print("1. 测试基本窗口")
    print("2. 测试表格组件")
    print("3. 测试文件对话框")
    print("4. 运行所有GUI测试")
    print("5. 退出")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == "1":
        test_basic_window()
    elif choice == "2":
        test_treeview_table()
    elif choice == "3":
        test_file_dialog()
    elif choice == "4":
        print("\n运行所有GUI测试...")
        test_basic_window()
        test_treeview_table()
        test_file_dialog()
    elif choice == "5":
        print("退出测试")
    else:
        print("无效选择")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
