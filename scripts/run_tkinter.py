#!/usr/bin/env python3
"""
tkinter版本启动脚本 - Python 3.13 + Miniconda
"""
import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def check_environment():
    """检查运行环境"""
    print("=" * 60)
    print("大乐透数据分析系统 - tkinter版本")
    print("=" * 60)
    
    # 检查Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 13:
        print("✓ Python版本符合要求")
    else:
        print("⚠ 建议使用Python 3.13+")
    
    # 检查Conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ Conda环境: {conda_env}")
    else:
        print("⚠ 未检测到Conda环境")
    
    return True


def check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包...")
    
    required_packages = [
        ("tkinter", "import tkinter as tk"),
        ("pandas", "import pandas as pd"),
        ("numpy", "import numpy as np"),
        ("dateutil", "import dateutil")
    ]
    
    missing_packages = []
    
    for package_name, import_code in required_packages:
        try:
            exec(import_code)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} 未安装")
            missing_packages.append(package_name)
    
    return len(missing_packages) == 0, missing_packages


def test_tkinter_gui():
    """测试tkinter GUI"""
    print("\n测试tkinter GUI...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("300x200")
        
        label = ttk.Label(root, text="tkinter GUI 正常工作！")
        label.pack(pady=50)
        
        button = ttk.Button(root, text="关闭", command=root.destroy)
        button.pack()
        
        print("✓ tkinter GUI测试窗口已创建")
        print("窗口将在3秒后自动关闭...")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ tkinter GUI测试失败: {e}")
        return False


def run_core_test():
    """运行核心功能测试"""
    print("\n" + "="*50)
    print("运行核心功能测试")
    print("="*50)
    
    try:
        # 切换到项目根目录
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        result = subprocess.run([
            sys.executable, os.path.join("scripts", "test_core.py")
        ], capture_output=True, text=True, cwd=root_dir)
        
        if result.returncode == 0:
            print("✓ 核心功能测试通过")
            return True
        else:
            print("✗ 核心功能测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False


def run_gui_app():
    """启动GUI应用程序"""
    print("\n" + "="*50)
    print("启动大乐透数据分析系统 (tkinter版本)")
    print("="*50)
    
    try:
        # 检查main.py是否存在
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        main_file = os.path.join(root_dir, "main.py")
        if not Path(main_file).exists():
            print("✗ main.py 文件不存在")
            return False
        
        print("正在启动GUI应用程序...")
        print("如果出现窗口，说明启动成功！")
        print("关闭窗口后程序将退出。")
        print("\n功能说明:")
        print("- 点击'导入数据'按钮选择CSV文件")
        print("- 系统会自动分析数据并显示结果")
        print("- 表格包含10列分析指标")
        
        # 启动GUI应用
        result = subprocess.run([sys.executable, "main.py"], cwd=root_dir)
        
        if result.returncode == 0:
            print("✓ 应用程序正常退出")
        else:
            print(f"⚠ 应用程序退出码: {result.returncode}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return True
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        return False


def show_usage_info():
    """显示使用说明"""
    print("\n" + "="*60)
    print("使用说明")
    print("="*60)
    print("1. 准备数据文件:")
    print("   - CSV格式，包含期号、开奖日期、红球1-5、蓝球1-2列")
    print("   - 示例: data/dlt_data.csv (已包含2753条历史数据)")
    
    print("\n2. 分析指标说明:")
    print("   - 奇偶比: 红球中奇数与偶数的比例")
    print("   - 奇偶排布: 红球奇偶数的具体排列模式")
    print("   - 分区比: 1-35号码分为7个区间的分布")
    print("   - 零分区比: 分区比中为0的区间分析")
    
    print("\n3. 操作步骤:")
    print("   - 启动程序后点击'导入数据'按钮")
    print("   - 选择CSV数据文件")
    print("   - 等待分析完成")
    print("   - 查看表格中的分析结果")


def main():
    """主函数"""
    # 检查环境
    if not check_environment():
        return
    
    # 检查依赖
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n缺少依赖包: {', '.join(missing)}")
        print("请使用以下命令安装:")
        for pkg in missing:
            if pkg == "tkinter":
                print("conda install tk")
            elif pkg in ["pandas", "numpy"]:
                print(f"conda install {pkg}")
            else:
                print(f"pip install {pkg}")
        return
    
    print("\n✓ 所有依赖包检查通过")
    
    # 显示使用说明
    show_usage_info()
    
    # 选择运行模式
    print("\n" + "="*60)
    print("选择运行模式:")
    print("1. 测试tkinter GUI")
    print("2. 运行核心功能测试")
    print("3. 启动完整应用程序")
    print("4. 先测试后启动应用")
    print("5. 退出")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == "1":
        test_tkinter_gui()
    elif choice == "2":
        run_core_test()
    elif choice == "3":
        run_gui_app()
    elif choice == "4":
        if test_tkinter_gui():
            if run_core_test():
                input("\n按Enter键启动完整应用程序...")
                run_gui_app()
            else:
                print("核心测试失败，建议先解决问题")
        else:
            print("GUI测试失败，建议检查tkinter安装")
    elif choice == "5":
        print("退出程序")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
