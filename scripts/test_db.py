#!/usr/bin/env python3
"""
数据库功能测试脚本
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model.db_manager import SQLiteManager
from model.data_model import LotteryDataModel
from analysis.analyzer import LotteryAnalyzer


def test_db_manager():
    """测试数据库管理器"""
    print("=" * 50)
    print("测试数据库管理器")
    print("=" * 50)
    
    # 创建测试数据库
    db_path = "data/test_analysis.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"已删除旧的测试数据库: {db_path}")
    
    # 创建数据库管理器
    db_manager = SQLiteManager(db_path)
    print(f"✓ 创建数据库管理器成功: {db_path}")
    
    # 检查表是否创建
    if db_manager.conn:
        cursor = db_manager.conn.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='analysis_results'"
        )
        if cursor.fetchone():
            print("✓ 表 analysis_results 已创建")
        else:
            print("✗ 表 analysis_results 创建失败")
    
    # 检查数据库是否为空
    if not db_manager.has_data():
        print("✓ 数据库为空")
    else:
        print("✗ 数据库不为空")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '日期': ['2023-01-01', '2023-01-02'],
        '期号': ['23001', '23002'],
        '红球': ['01,02,03,04,05', '06,07,08,09,10'],
        '蓝球': ['01,02', '03,04'],
        '奇偶比': ['3:2', '2:3'],
        '奇偶排布': ['奇奇奇偶偶', '偶偶奇奇偶'],
        '上次奇偶排布': ['无', '2023-01-01(1天)'],
        '分区比': ['1:1:1:1:1:0:0', '0:2:1:1:1:0:0'],
        '上次分区比': ['无', '无'],
        '上次0分区比': ['无', '无']
    })
    
    # 保存测试数据
    success = db_manager.save_results(test_data)
    if success:
        print(f"✓ 保存测试数据成功: {len(test_data)} 条记录")
    else:
        print("✗ 保存测试数据失败")
    
    # 检查数据库是否有数据
    if db_manager.has_data():
        print("✓ 数据库有数据")
    else:
        print("✗ 数据库没有数据")
    
    # 加载数据
    loaded_data = db_manager.load_results()
    if loaded_data is not None and len(loaded_data) == len(test_data):
        print(f"✓ 加载数据成功: {len(loaded_data)} 条记录")
        print("\n前2条数据:")
        print(loaded_data.head(2))
    else:
        print("✗ 加载数据失败")
    
    # 获取统计信息
    stats = db_manager.get_statistics()
    print(f"\n数据库统计信息:")
    print(f"总记录数: {stats['total_records']}")
    print(f"开始日期: {stats['start_date']}")
    print(f"结束日期: {stats['end_date']}")
    
    # 重建数据库
    new_test_data = pd.DataFrame({
        '日期': ['2023-02-01', '2023-02-02', '2023-02-03'],
        '期号': ['23003', '23004', '23005'],
        '红球': ['11,12,13,14,15', '16,17,18,19,20', '21,22,23,24,25'],
        '蓝球': ['05,06', '07,08', '09,10'],
        '奇偶比': ['5:0', '0:5', '3:2'],
        '奇偶排布': ['奇奇奇奇奇', '偶偶偶偶偶', '奇奇奇偶偶'],
        '上次奇偶排布': ['无', '无', '2023-01-01(33天)'],
        '分区比': ['0:0:5:0:0:0:0', '0:0:0:5:0:0:0', '0:0:0:0:5:0:0'],
        '上次分区比': ['无', '无', '无'],
        '上次0分区比': ['无', '无', '无']
    })
    
    success = db_manager.rebuild_database(new_test_data)
    if success:
        print(f"\n✓ 重建数据库成功: {len(new_test_data)} 条记录")
    else:
        print("\n✗ 重建数据库失败")
    
    # 再次加载数据
    loaded_data = db_manager.load_results()
    if loaded_data is not None and len(loaded_data) == len(new_test_data):
        print(f"✓ 重建后加载数据成功: {len(loaded_data)} 条记录")
        print("\n前3条数据:")
        print(loaded_data.head(3))
    else:
        print("✗ 重建后加载数据失败")
    
    # 清理
    db_manager.disconnect()
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"\n已删除测试数据库: {db_path}")
    
    print("\n✓ 数据库管理器测试完成")


def test_data_model_with_db():
    """测试数据模型与数据库集成"""
    print("\n" + "=" * 50)
    print("测试数据模型与数据库集成")
    print("=" * 50)
    
    # 确保测试数据库不存在
    db_path = "data/analysis_results.db"
    if os.path.exists(db_path):
        os.rename(db_path, f"{db_path}.bak")
        print(f"已备份现有数据库: {db_path} -> {db_path}.bak")
    
    # 创建数据模型
    data_model = LotteryDataModel()
    print("✓ 创建数据模型成功")
    
    # 检查是否有数据
    if data_model.has_database_data():
        print("✗ 数据库应该为空")
    else:
        print("✓ 数据库为空")
    
    # 加载CSV数据
    csv_path = "data/dlt_data.csv"
    if os.path.exists(csv_path):
        success = data_model.load_csv_data(csv_path)
        if success:
            print(f"✓ 加载CSV数据成功: {data_model.get_data_count()} 条记录")
        else:
            print("✗ 加载CSV数据失败")
    else:
        print(f"✗ CSV文件不存在: {csv_path}")
        return
    
    # 分析数据
    analyzer = LotteryAnalyzer()
    raw_data = data_model.get_raw_data()
    analyzed_data = analyzer.analyze_data(raw_data)
    
    if analyzed_data is not None:
        print(f"✓ 分析数据成功: {len(analyzed_data)} 条记录")
    else:
        print("✗ 分析数据失败")
        return
    
    # 保存到数据库
    success = data_model.save_to_database(analyzed_data)
    if success:
        print(f"✓ 保存分析结果到数据库成功")
    else:
        print("✗ 保存分析结果到数据库失败")
    
    # 检查数据库是否有数据
    if data_model.has_database_data():
        print("✓ 数据库有数据")
    else:
        print("✗ 数据库应该有数据")
    
    # 获取统计信息
    stats = data_model.get_database_statistics()
    print(f"\n数据库统计信息:")
    print(f"总记录数: {stats['total_records']}")
    print(f"开始日期: {stats['start_date']}")
    print(f"结束日期: {stats['end_date']}")
    
    # 恢复原始数据库
    if os.path.exists(db_path):
        os.remove(db_path)
    
    if os.path.exists(f"{db_path}.bak"):
        os.rename(f"{db_path}.bak", db_path)
        print(f"已恢复原始数据库: {db_path}.bak -> {db_path}")
    
    print("\n✓ 数据模型与数据库集成测试完成")


def main():
    """主函数"""
    print("大乐透数据分析系统 - 数据库功能测试")
    
    # 测试数据库管理器
    test_db_manager()
    
    # 测试数据模型与数据库集成
    test_data_model_with_db()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
