#!/usr/bin/env python3
"""
Python 3.13 专用启动脚本
适用于Miniconda环境
"""
import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 13:
        print("✓ Python版本符合要求")
        return True
    else:
        print("✗ 需要Python 3.13+")
        return False


def check_conda_env():
    """检查Conda环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ 当前Conda环境: {conda_env}")
        return True
    else:
        print("⚠ 未检测到Conda环境")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包...")
    
    required_packages = [
        ("pandas", "import pandas as pd"),
        ("numpy", "import numpy as np"), 
        ("PyQt5", "from PyQt5.QtWidgets import QApplication"),
        ("dateutil", "import dateutil")
    ]
    
    missing_packages = []
    
    for package_name, import_code in required_packages:
        try:
            exec(import_code)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} 未安装")
            missing_packages.append(package_name)
    
    return len(missing_packages) == 0, missing_packages


def install_missing_packages(missing_packages):
    """安装缺失的包"""
    print(f"\n发现缺失的包: {', '.join(missing_packages)}")
    
    install_commands = {
        'pandas': 'conda install -y pandas',
        'numpy': 'conda install -y numpy',
        'PyQt5': 'conda install -y -c conda-forge pyqt',
        'dateutil': 'conda install -y python-dateutil'
    }
    
    for package in missing_packages:
        if package in install_commands:
            print(f"\n安装 {package}...")
            try:
                result = subprocess.run(
                    install_commands[package].split(),
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    print(f"✓ {package} 安装成功")
                else:
                    print(f"✗ {package} 安装失败")
                    return False
            except Exception as e:
                print(f"✗ 安装 {package} 时出错: {e}")
                return False
    
    return True


def run_core_test():
    """运行核心功能测试"""
    print("\n" + "="*50)
    print("运行核心功能测试")
    print("="*50)
    
    try:
        result = subprocess.run([
            sys.executable, "test_core.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 核心功能测试通过")
            return True
        else:
            print("✗ 核心功能测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False


def run_gui_app():
    """启动GUI应用程序"""
    print("\n" + "="*50)
    print("启动大乐透数据分析系统")
    print("="*50)
    
    try:
        # 检查main.py是否存在
        if not Path("main.py").exists():
            print("✗ main.py 文件不存在")
            return False
        
        print("正在启动GUI应用程序...")
        print("如果出现窗口，说明启动成功！")
        print("关闭窗口后程序将退出。")
        
        # 启动GUI应用
        result = subprocess.run([sys.executable, "main.py"])
        
        if result.returncode == 0:
            print("✓ 应用程序正常退出")
        else:
            print(f"⚠ 应用程序退出码: {result.returncode}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return True
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        return False


def main():
    """主函数"""
    print("="*60)
    print("大乐透数据分析系统 - Python 3.13 启动器")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        print("\n请使用Python 3.13+版本")
        return
    
    # 检查Conda环境
    check_conda_env()
    
    # 检查依赖包
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n缺少依赖包: {', '.join(missing)}")
        install_choice = input("是否自动安装缺失的包? (y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_missing_packages(missing):
                print("依赖包安装失败，无法继续")
                return
        else:
            print("请手动安装缺失的包后再运行")
            return
    
    print("\n✓ 所有依赖包检查通过")
    
    # 询问运行模式
    print("\n选择运行模式:")
    print("1. 运行核心功能测试")
    print("2. 启动GUI应用程序")
    print("3. 先测试后启动GUI")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        run_core_test()
    elif choice == "2":
        run_gui_app()
    elif choice == "3":
        if run_core_test():
            input("\n按Enter键启动GUI应用程序...")
            run_gui_app()
        else:
            print("核心测试失败，建议先解决问题")
    elif choice == "4":
        print("退出程序")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
