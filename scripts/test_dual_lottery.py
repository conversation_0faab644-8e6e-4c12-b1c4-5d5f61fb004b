#!/usr/bin/env python3
"""
双彩票类型功能测试脚本
"""
import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.lottery_config import LotteryType, LotteryConfig
from model.data_model import LotteryDataModel
from analysis.analyzer import LotteryAnalyzer
from model.db_manager import SQLiteManager


def test_lottery_config():
    """测试彩票配置"""
    print("=" * 60)
    print("测试彩票配置")
    print("=" * 60)
    
    # 测试大乐透配置
    dlt_config = LotteryConfig.get_config(LotteryType.DLT)
    print(f"大乐透配置:")
    print(f"  名称: {dlt_config['name']}")
    print(f"  红球范围: {dlt_config['red_range']}")
    print(f"  红球数量: {dlt_config['red_count']}")
    print(f"  蓝球范围: {dlt_config['blue_range']}")
    print(f"  蓝球数量: {dlt_config['blue_count']}")
    print(f"  分区大小: {dlt_config['zone_sizes']}")
    print(f"  CSV文件: {dlt_config['csv_file']}")
    
    # 测试双色球配置
    ssq_config = LotteryConfig.get_config(LotteryType.SSQ)
    print(f"\n双色球配置:")
    print(f"  名称: {ssq_config['name']}")
    print(f"  红球范围: {ssq_config['red_range']}")
    print(f"  红球数量: {ssq_config['red_count']}")
    print(f"  蓝球范围: {ssq_config['blue_range']}")
    print(f"  蓝球数量: {ssq_config['blue_count']}")
    print(f"  分区大小: {ssq_config['zone_sizes']}")
    print(f"  CSV文件: {ssq_config['csv_file']}")
    
    # 测试分区边界计算
    dlt_boundaries = LotteryConfig.calculate_zone_boundaries(LotteryType.DLT)
    ssq_boundaries = LotteryConfig.calculate_zone_boundaries(LotteryType.SSQ)
    
    print(f"\n大乐透分区边界: {dlt_boundaries}")
    print(f"双色球分区边界: {ssq_boundaries}")
    
    print("\n✓ 彩票配置测试完成")


def test_data_loading():
    """测试数据加载"""
    print("\n" + "=" * 60)
    print("测试数据加载")
    print("=" * 60)
    
    data_model = LotteryDataModel()
    
    # 测试大乐透数据加载
    print("测试大乐透数据加载...")
    dlt_file = LotteryConfig.get_csv_file(LotteryType.DLT)
    if os.path.exists(dlt_file):
        success = data_model.load_csv_data(dlt_file, LotteryType.DLT)
        if success:
            print(f"✓ 大乐透数据加载成功: {data_model.get_data_count()} 条记录")
        else:
            print("✗ 大乐透数据加载失败")
    else:
        print(f"✗ 大乐透数据文件不存在: {dlt_file}")
    
    # 测试双色球数据加载
    print("\n测试双色球数据加载...")
    ssq_file = LotteryConfig.get_csv_file(LotteryType.SSQ)
    if os.path.exists(ssq_file):
        success = data_model.load_csv_data(ssq_file, LotteryType.SSQ)
        if success:
            print(f"✓ 双色球数据加载成功: {data_model.get_data_count()} 条记录")
            
            # 测试数据提取
            if data_model.get_data_count() > 0:
                red_balls = data_model.get_red_balls(0)
                blue_balls = data_model.get_blue_balls(0)
                print(f"  第一条记录 - 红球: {red_balls}, 蓝球: {blue_balls}")
        else:
            print("✗ 双色球数据加载失败")
    else:
        print(f"✗ 双色球数据文件不存在: {ssq_file}")
    
    print("\n✓ 数据加载测试完成")


def test_analysis():
    """测试数据分析"""
    print("\n" + "=" * 60)
    print("测试数据分析")
    print("=" * 60)
    
    data_model = LotteryDataModel()
    analyzer = LotteryAnalyzer()
    
    # 测试双色球分析
    print("测试双色球分析...")
    ssq_file = LotteryConfig.get_csv_file(LotteryType.SSQ)
    if os.path.exists(ssq_file):
        success = data_model.load_csv_data(ssq_file, LotteryType.SSQ)
        if success:
            # 只分析前10条数据以节省时间
            raw_data = data_model.get_raw_data().head(10)
            analyzed_data = analyzer.analyze_data(raw_data, LotteryType.SSQ)
            
            if analyzed_data is not None and len(analyzed_data) > 0:
                print(f"✓ 双色球分析成功: {len(analyzed_data)} 条记录")
                print("\n前3条分析结果:")
                for i in range(min(3, len(analyzed_data))):
                    row = analyzed_data.iloc[i]
                    print(f"  {i+1}. 日期:{row['日期']} 期号:{row['期号']} "
                          f"红球:{row['红球']} 蓝球:{row['蓝球']} "
                          f"奇偶比:{row['奇偶比']} 分区比:{row['分区比']}")
            else:
                print("✗ 双色球分析失败")
        else:
            print("✗ 双色球数据加载失败")
    else:
        print(f"✗ 双色球数据文件不存在: {ssq_file}")
    
    print("\n✓ 数据分析测试完成")


def test_database():
    """测试数据库功能"""
    print("\n" + "=" * 60)
    print("测试数据库功能")
    print("=" * 60)
    
    # 创建测试数据库
    test_db_path = "data/test_dual_lottery.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    db_manager = SQLiteManager(test_db_path)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '日期': ['2023-01-01', '2023-01-02'],
        '期号': ['2023001', '2023002'],
        '红球': ['01,02,03,04,05,06', '07,08,09,10,11,12'],
        '蓝球': ['01', '02'],
        '奇偶比': ['3:3', '0:6'],
        '奇偶排布': ['奇偶奇偶奇偶', '偶偶奇偶奇偶'],
        '上次奇偶排布': ['无', '无'],
        '分区比': ['1:1:1:1:1:1:0', '0:2:2:2:0:0:0'],
        '上次分区比': ['无', '无'],
        '上次0分区比': ['无', '无']
    })
    
    # 测试保存双色球数据
    success = db_manager.save_results(test_data, LotteryType.SSQ)
    if success:
        print("✓ 双色球数据保存成功")
    else:
        print("✗ 双色球数据保存失败")
    
    # 测试加载双色球数据
    loaded_data = db_manager.load_results(LotteryType.SSQ)
    if loaded_data is not None and len(loaded_data) == len(test_data):
        print("✓ 双色球数据加载成功")
    else:
        print("✗ 双色球数据加载失败")
    
    # 测试统计信息
    stats = db_manager.get_statistics(LotteryType.SSQ)
    print(f"双色球数据库统计: {stats}")
    
    # 清理测试数据库
    db_manager.disconnect()
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    print("\n✓ 数据库功能测试完成")


def test_zone_analysis():
    """测试分区分析差异"""
    print("\n" + "=" * 60)
    print("测试分区分析差异")
    print("=" * 60)
    
    from analysis.zone_analysis import calculate_zone_distribution
    
    # 测试相同号码在不同彩票类型下的分区分布
    test_balls = [1, 6, 11, 16, 21, 26]  # 跨越多个区间的号码
    
    dlt_zones = calculate_zone_distribution(test_balls[:5], LotteryType.DLT)  # 大乐透5个红球
    ssq_zones = calculate_zone_distribution(test_balls, LotteryType.SSQ)      # 双色球6个红球
    
    print(f"测试号码: {test_balls}")
    print(f"大乐透分区分布 (前5个球): {dlt_zones}")
    print(f"双色球分区分布 (全部6个球): {ssq_zones}")
    
    # 测试边界号码
    boundary_balls_dlt = [5, 10, 15, 20, 25]  # 大乐透边界号码
    boundary_balls_ssq = [5, 10, 15, 20, 25, 30]  # 双色球边界号码
    
    dlt_boundary_zones = calculate_zone_distribution(boundary_balls_dlt, LotteryType.DLT)
    ssq_boundary_zones = calculate_zone_distribution(boundary_balls_ssq, LotteryType.SSQ)
    
    print(f"\n边界测试:")
    print(f"大乐透边界号码 {boundary_balls_dlt}: {dlt_boundary_zones}")
    print(f"双色球边界号码 {boundary_balls_ssq}: {ssq_boundary_zones}")
    
    print("\n✓ 分区分析差异测试完成")


def main():
    """主函数"""
    print("双彩票类型功能测试")
    print("测试大乐透和双色球支持")
    
    try:
        # 测试彩票配置
        test_lottery_config()
        
        # 测试数据加载
        test_data_loading()
        
        # 测试数据分析
        test_analysis()
        
        # 测试数据库功能
        test_database()
        
        # 测试分区分析差异
        test_zone_analysis()
        
        print("\n" + "=" * 60)
        print("所有测试完成")
        print("=" * 60)
        print("✓ 双彩票类型功能正常工作")
        print("您现在可以运行主程序测试GUI界面:")
        print("python main.py")
        
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
