#!/usr/bin/env python3
"""
带数据库功能的启动脚本
"""
import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def check_database():
    """检查数据库状态"""
    print("=" * 60)
    print("检查数据库状态")
    print("=" * 60)
    
    try:
        from model.db_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        
        if db_manager.has_data():
            stats = db_manager.get_statistics()
            print(f"✓ 数据库有数据")
            print(f"  总记录数: {stats['total_records']}")
            print(f"  数据范围: {stats['start_date']} 至 {stats['end_date']}")
            return True
        else:
            print("⚠ 数据库为空")
            return False
            
    except Exception as e:
        print(f"✗ 检查数据库失败: {e}")
        return False


def test_database_functionality():
    """测试数据库功能"""
    print("\n" + "=" * 60)
    print("测试数据库功能")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, "scripts/test_db.py"
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        if result.returncode == 0:
            print("✓ 数据库功能测试通过")
            return True
        else:
            print("✗ 数据库功能测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False


def run_gui_app():
    """启动GUI应用程序"""
    print("\n" + "=" * 60)
    print("启动大乐透数据分析系统 (带数据库功能)")
    print("=" * 60)
    
    try:
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        main_file = os.path.join(root_dir, "main.py")
        
        if not Path(main_file).exists():
            print("✗ main.py 文件不存在")
            return False
        
        print("正在启动GUI应用程序...")
        print("\n功能说明:")
        print("- 启动时自动从数据库加载已分析的数据")
        print("- 点击'导入数据(重新分析)'按钮可重新分析并更新数据库")
        print("- 所有分析结果都会自动保存到SQLite数据库中")
        print("- 数据库文件位置: data/analysis_results.db")
        
        # 启动GUI应用
        result = subprocess.run([sys.executable, "main.py"], cwd=root_dir)
        
        if result.returncode == 0:
            print("✓ 应用程序正常退出")
        else:
            print(f"⚠ 应用程序退出码: {result.returncode}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return True
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        return False


def show_database_info():
    """显示数据库信息"""
    print("\n" + "=" * 60)
    print("数据库功能说明")
    print("=" * 60)
    
    print("1. 数据存储:")
    print("   - 所有分析结果自动保存到SQLite数据库")
    print("   - 数据库文件: data/analysis_results.db")
    print("   - 包含完整的10列分析指标")
    
    print("\n2. 启动行为:")
    print("   - 应用启动时优先从数据库加载数据")
    print("   - 如果数据库为空，会提示导入CSV文件")
    
    print("\n3. 导入功能:")
    print("   - '导入数据(重新分析)'按钮会重新计算并更新数据库")
    print("   - 旧数据会被清空，新数据会重新分析并保存")
    
    print("\n4. 性能优化:")
    print("   - 避免重复计算，提高启动速度")
    print("   - 支持大量历史数据的高效存储和查询")


def main():
    """主函数"""
    print("大乐透数据分析系统 - 数据库版本启动器")
    print("适用于Python 3.13 + SQLite3")
    
    # 检查数据库状态
    has_data = check_database()
    
    # 显示数据库信息
    show_database_info()
    
    # 选择运行模式
    print("\n" + "=" * 60)
    print("选择运行模式:")
    print("1. 直接启动应用程序")
    print("2. 测试数据库功能")
    print("3. 先测试后启动应用")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        run_gui_app()
    elif choice == "2":
        test_database_functionality()
    elif choice == "3":
        if test_database_functionality():
            input("\n按Enter键启动GUI应用程序...")
            run_gui_app()
        else:
            print("数据库测试失败，建议先解决问题")
    elif choice == "4":
        print("退出程序")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
