#!/usr/bin/env python3
"""
高级集成预测系统
基于o3-mini建议，实现多模型融合、动态权重调整和概率建模改进
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import json
from datetime import datetime
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from enhanced_multidimensional_predictor import EnhancedMultidimensionalPredictor


class AdvancedFeatureExtractor:
    """高级特征提取器 - 实现多视角特征构建"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        
    def extract_time_series_features(self, data: List[Dict], window_size: int = 20) -> np.ndarray:
        """提取时序特征"""
        features = []
        
        for i in range(len(data)):
            if i < window_size:
                continue
                
            window_data = data[i-window_size:i]
            
            # 基础统计特征
            zone_counts = []
            zero_counts = []
            
            for record in window_data:
                zone_dist = self._parse_zone_ratio(record['分区比'])
                zone_counts.append(zone_dist)
                zero_counts.append(sum(1 for x in zone_dist if x == 0))
            
            zone_matrix = np.array(zone_counts)
            
            # 时序特征
            feature_vector = []
            
            # 1. 移动平均和方差
            for zone in range(7):
                zone_series = zone_matrix[:, zone]
                feature_vector.extend([
                    np.mean(zone_series),
                    np.std(zone_series),
                    np.mean(zone_series[-5:]),  # 短期均值
                    np.std(zone_series[-5:])    # 短期方差
                ])
            
            # 2. 趋势特征
            zero_series = np.array(zero_counts)
            feature_vector.extend([
                np.mean(zero_series),
                np.std(zero_series),
                zero_series[-1] - zero_series[-5],  # 短期变化
                np.corrcoef(np.arange(len(zero_series)), zero_series)[0, 1] if len(zero_series) > 1 else 0  # 趋势相关性
            ])
            
            # 3. 周期性特征
            for period in [3, 5, 7]:
                if len(zero_series) >= period * 2:
                    period_corr = np.corrcoef(zero_series[:-period], zero_series[period:])[0, 1]
                    feature_vector.append(period_corr if not np.isnan(period_corr) else 0)
                else:
                    feature_vector.append(0)
            
            # 4. 分布特征
            for zone in range(7):
                zone_series = zone_matrix[:, zone]
                feature_vector.extend([
                    np.percentile(zone_series, 25),
                    np.percentile(zone_series, 75),
                    len(np.where(zone_series == 0)[0]) / len(zone_series)  # 0的比例
                ])
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def extract_pattern_features(self, data: List[Dict], window_size: int = 30) -> np.ndarray:
        """提取模式特征"""
        features = []
        
        for i in range(len(data)):
            if i < window_size:
                continue
                
            window_data = data[i-window_size:i]
            feature_vector = []
            
            # 模式统计
            patterns = []
            for record in window_data:
                zone_dist = self._parse_zone_ratio(record['分区比'])
                pattern = ''.join(['0' if x == 0 else 'x' for x in zone_dist])
                patterns.append(pattern)
            
            pattern_counts = Counter(patterns)
            
            # 模式多样性
            feature_vector.append(len(pattern_counts))  # 唯一模式数
            feature_vector.append(max(pattern_counts.values()) / len(patterns))  # 最频繁模式比例
            
            # 连续性特征
            consecutive_same = 0
            max_consecutive = 0
            for j in range(1, len(patterns)):
                if patterns[j] == patterns[j-1]:
                    consecutive_same += 1
                    max_consecutive = max(max_consecutive, consecutive_same + 1)
                else:
                    consecutive_same = 0
            
            feature_vector.append(max_consecutive)
            
            # 最近模式特征
            recent_patterns = patterns[-5:]
            recent_pattern_counts = Counter(recent_patterns)
            feature_vector.append(len(recent_pattern_counts))
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def _parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7


class ProbabilisticPredictor:
    """概率预测器 - 实现贝叶斯建模和不确定性量化"""
    
    def __init__(self, n_estimators: int = 100):
        self.n_estimators = n_estimators
        self.models = []
        self.feature_extractor = AdvancedFeatureExtractor()
        
    def fit(self, data: List[Dict]):
        """训练概率模型"""
        # 提取特征
        time_features = self.feature_extractor.extract_time_series_features(data)
        pattern_features = self.feature_extractor.extract_pattern_features(data)
        
        if len(time_features) == 0 or len(pattern_features) == 0:
            return
        
        # 对齐特征
        min_len = min(len(time_features), len(pattern_features))
        features = np.hstack([time_features[-min_len:], pattern_features[-min_len:]])
        
        # 准备目标变量
        targets = []
        for i in range(len(data) - len(features), len(data)):
            zone_dist = self.feature_extractor._parse_zone_ratio(data[i]['分区比'])
            targets.append(zone_dist)
        
        targets = np.array(targets)
        
        # 训练多个模型（模拟贝叶斯采样）
        self.models = []
        for i in range(self.n_estimators):
            # 随机采样训练数据
            n_samples = len(features)
            indices = np.random.choice(n_samples, size=int(n_samples * 0.8), replace=True)
            
            X_sample = features[indices]
            y_sample = targets[indices]
            
            # 为每个位置训练一个模型
            position_models = []
            for pos in range(7):
                model = RandomForestRegressor(n_estimators=50, random_state=i, max_depth=10)
                model.fit(X_sample, y_sample[:, pos])
                position_models.append(model)
            
            self.models.append(position_models)
    
    def predict_with_uncertainty(self, data: List[Dict], n_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """带不确定性的预测"""
        if not self.models:
            return [("xx0x0x0", 0.1, {})] * n_predictions
        
        # 提取最新特征
        time_features = self.feature_extractor.extract_time_series_features(data)
        pattern_features = self.feature_extractor.extract_pattern_features(data)
        
        if len(time_features) == 0 or len(pattern_features) == 0:
            return [("xx0x0x0", 0.1, {})] * n_predictions
        
        # 使用最新特征
        latest_features = np.hstack([time_features[-1:], pattern_features[-1:]])
        
        # 收集所有模型的预测
        all_predictions = []
        for model_set in self.models:
            prediction = []
            for pos, model in enumerate(model_set):
                pred_value = model.predict(latest_features)[0]
                prediction.append(max(0, pred_value))
            all_predictions.append(prediction)
        
        all_predictions = np.array(all_predictions)
        
        # 生成多个预测结果
        predictions = []
        for i in range(n_predictions):
            # 随机选择一组预测
            selected_pred = all_predictions[np.random.choice(len(all_predictions))]
            
            # 转换为0/x模式
            pattern = self._convert_to_pattern(selected_pred)
            
            # 计算置信度（基于预测方差）
            pred_std = np.std(all_predictions, axis=0)
            confidence = 1 / (1 + np.mean(pred_std))
            
            # 添加随机性
            confidence += np.random.normal(0, 0.05)
            confidence = max(0.1, min(0.9, confidence))
            
            info = {
                'probabilistic_prediction': True,
                'prediction_variance': np.mean(pred_std),
                'model_count': len(self.models)
            }
            
            predictions.append((pattern, confidence, info))
        
        # 按置信度排序
        predictions.sort(key=lambda x: x[1], reverse=True)
        return predictions
    
    def _convert_to_pattern(self, prediction: np.ndarray) -> str:
        """将数值预测转换为0/x模式"""
        # 使用阈值方法
        threshold = np.mean(prediction) * 0.5
        pattern = ''.join(['0' if x < threshold else 'x' for x in prediction])
        
        # 确保有合理数量的0
        zero_count = pattern.count('0')
        if zero_count < 2:
            # 强制设置最小的两个为0
            indices = np.argsort(prediction)[:2]
            pattern_list = list(pattern)
            for idx in indices:
                pattern_list[idx] = '0'
            pattern = ''.join(pattern_list)
        elif zero_count > 5:
            # 强制设置最大的两个为x
            indices = np.argsort(prediction)[-2:]
            pattern_list = list(pattern)
            for idx in indices:
                pattern_list[idx] = 'x'
            pattern = ''.join(pattern_list)
        
        return pattern


class DynamicWeightManager:
    """动态权重管理器 - 实现自适应权重调整"""
    
    def __init__(self, window_size: int = 10):
        self.window_size = window_size
        self.performance_history = defaultdict(list)
        
    def update_performance(self, predictor_name: str, accuracy: float):
        """更新预测器性能"""
        self.performance_history[predictor_name].append(accuracy)
        if len(self.performance_history[predictor_name]) > self.window_size:
            self.performance_history[predictor_name].pop(0)
    
    def get_dynamic_weights(self) -> Dict[str, float]:
        """获取动态权重"""
        if not self.performance_history:
            return {}
        
        weights = {}
        total_score = 0
        
        for predictor_name, scores in self.performance_history.items():
            if scores:
                # 使用指数加权平均，更重视近期表现
                weights_exp = np.exp(np.arange(len(scores)) * 0.1)
                weighted_score = np.average(scores, weights=weights_exp)
                weights[predictor_name] = weighted_score
                total_score += weighted_score
        
        # 归一化权重
        if total_score > 0:
            for name in weights:
                weights[name] = weights[name] / total_score
        
        return weights


class AdvancedEnsemblePredictor:
    """高级集成预测器 - 整合多种预测策略"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        np.random.seed(seed)
        
        # 初始化子预测器
        self.base_predictor = EnhancedMultidimensionalPredictor(seed=seed)
        self.probabilistic_predictor = ProbabilisticPredictor()
        self.weight_manager = DynamicWeightManager()
        
        # 预测器权重
        self.predictor_weights = {
            'base': 0.4,
            'probabilistic': 0.6
        }
        
    def train(self, data: List[Dict]):
        """训练所有子预测器"""
        print("训练概率预测器...")
        self.probabilistic_predictor.fit(data)
        print("训练完成")
    
    def predict_ensemble(self, data: List[Dict], n_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """集成预测"""
        # 获取各预测器的结果
        base_predictions = self.base_predictor.predict_with_multidimensional_enhancement(data[-50:], n_predictions)
        prob_predictions = self.probabilistic_predictor.predict_with_uncertainty(data, n_predictions)
        
        # 动态权重
        dynamic_weights = self.weight_manager.get_dynamic_weights()
        if dynamic_weights:
            base_weight = dynamic_weights.get('base', self.predictor_weights['base'])
            prob_weight = dynamic_weights.get('probabilistic', self.predictor_weights['probabilistic'])
        else:
            base_weight = self.predictor_weights['base']
            prob_weight = self.predictor_weights['probabilistic']
        
        # 融合预测结果
        ensemble_predictions = []
        
        for i in range(n_predictions):
            # 选择预测结果
            if np.random.random() < base_weight:
                if i < len(base_predictions):
                    pattern, confidence, info = base_predictions[i]
                    info['source'] = 'base'
                else:
                    pattern, confidence, info = base_predictions[0]
                    info['source'] = 'base'
            else:
                if i < len(prob_predictions):
                    pattern, confidence, info = prob_predictions[i]
                    info['source'] = 'probabilistic'
                else:
                    pattern, confidence, info = prob_predictions[0]
                    info['source'] = 'probabilistic'
            
            # 调整置信度
            ensemble_confidence = confidence * 0.8 + np.random.normal(0, 0.05)
            ensemble_confidence = max(0.1, min(0.9, ensemble_confidence))
            
            info['ensemble_weights'] = {'base': base_weight, 'probabilistic': prob_weight}
            info['dynamic_adjustment'] = bool(dynamic_weights)
            
            ensemble_predictions.append((pattern, ensemble_confidence, info))
        
        # 去重并排序
        unique_predictions = []
        seen_patterns = set()
        
        for pattern, confidence, info in ensemble_predictions:
            if pattern not in seen_patterns:
                unique_predictions.append((pattern, confidence, info))
                seen_patterns.add(pattern)
        
        # 按置信度排序
        unique_predictions.sort(key=lambda x: x[1], reverse=True)
        
        return unique_predictions[:n_predictions]


class AdvancedEnsembleTester:
    """高级集成预测测试器"""

    def __init__(self):
        self.predictor = AdvancedEnsemblePredictor(seed=42)

    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7

    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])

    def run_advanced_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 300):
        """运行高级集成预测测试"""
        print("=" * 80)
        print("高级集成预测系统测试")
        print("=" * 80)

        # 准备数据
        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")

        # 训练模型
        print("\n开始训练高级集成模型...")
        self.predictor.train(train_data)

        print(f"\n预测结果:")
        print("-" * 80)

        # 统计变量
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        position_matches = [0] * 7
        confidence_scores = []
        source_stats = {'base': 0, 'probabilistic': 0}

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]

            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)

            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")

            # 生成集成预测
            predictions = self.predictor.predict_ensemble(history, n_predictions=5)

            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')

                # 检查完全匹配
                if match_count == 7:
                    perfect_matches += 1
                    break

                if j == 0:  # 统计第一个预测
                    total_matches += match_count
                    confidence_scores.append(confidence)

                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1

                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1

                    # 统计预测来源
                    source = info.get('source', 'unknown')
                    if source in source_stats:
                        source_stats[source] += 1

                # 显示预测信息
                source_info = f"来源:{info.get('source', 'unknown')}"
                ensemble_info = ""
                if info.get('ensemble_weights'):
                    weights = info['ensemble_weights']
                    ensemble_info = f"  权重(基础:{weights['base']:.2f},概率:{weights['probabilistic']:.2f})"

                print(f"集成预测：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}  {source_info}{ensemble_info}")

            # 更新动态权重（基于第一个预测的表现）
            if predictions:
                first_pred = predictions[0]
                first_match_count = sum(1 for p, a in zip(first_pred[0], actual_pattern) if p == a)
                accuracy = first_match_count / 7
                source = first_pred[2].get('source', 'unknown')
                self.predictor.weight_manager.update_performance(source, accuracy)

            print()

        # 统计结果
        print(f"=" * 80)
        print("高级集成预测效果分析")
        print("=" * 80)

        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        perfect_accuracy = perfect_matches / test_periods
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0

        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        print(f"  完全匹配率(7/7): {perfect_accuracy:.1%} ({perfect_matches}/{test_periods}期)")
        print(f"  平均置信度: {avg_confidence:.3f}")
        print(f"  置信度稳定性: {confidence_stability:.3f}")

        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")

        print(f"\n预测来源统计:")
        total_sources = sum(source_stats.values())
        for source, count in source_stats.items():
            percentage = count / total_sources * 100 if total_sources > 0 else 0
            print(f"  {source}: {count}次 ({percentage:.1f}%)")

        print(f"\n动态权重状态:")
        dynamic_weights = self.predictor.weight_manager.get_dynamic_weights()
        if dynamic_weights:
            for name, weight in dynamic_weights.items():
                print(f"  {name}: {weight:.3f}")
        else:
            print("  使用默认权重")

        # 计算综合评分
        ensemble_score = (avg_accuracy * 0.4 + zero_accuracy * 0.3 +
                         perfect_accuracy * 0.2 + confidence_stability * 0.1)
        print(f"\n综合评分: {ensemble_score:.4f}")

        return {
            'position_accuracy': avg_accuracy,
            'zero_count_accuracy': zero_accuracy,
            'perfect_match_rate': perfect_accuracy,
            'confidence': avg_confidence,
            'confidence_stability': confidence_stability,
            'ensemble_score': ensemble_score,
            'source_distribution': source_stats
        }


def main():
    """主函数"""
    try:
        # 加载数据
        print("加载历史数据...")
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')

        if len(data) < 400:
            print("数据不足，需要至少400期数据")
            return

        print(f"已加载 {len(data)} 期数据")

        # 运行高级集成预测测试
        tester = AdvancedEnsembleTester()
        results = tester.run_advanced_test(data, test_periods=20, train_periods=300)

        print("\n高级集成预测系统测试完成！")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
