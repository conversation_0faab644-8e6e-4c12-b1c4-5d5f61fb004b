"""
分析模块单元测试
"""
import unittest
import pandas as pd
from datetime import datetime

from analysis.odd_even_analysis import (
    calculate_odd_even_ratio, 
    calculate_odd_even_pattern,
    analyze_odd_even
)
from analysis.zone_analysis import (
    calculate_zone_distribution,
    format_zone_ratio,
    get_zero_zones_pattern,
    analyze_zones
)
from analysis.base_info import extract_basic_info


class TestOddEvenAnalysis(unittest.TestCase):
    """奇偶分析测试"""
    
    def test_calculate_odd_even_ratio(self):
        """测试奇偶比计算"""
        # 测试3奇2偶
        red_balls = [1, 3, 5, 8, 10]
        result = calculate_odd_even_ratio(red_balls)
        self.assertEqual(result, "3:2")
        
        # 测试2奇3偶
        red_balls = [2, 4, 6, 7, 9]
        result = calculate_odd_even_ratio(red_balls)
        self.assertEqual(result, "2:3")
    
    def test_calculate_odd_even_pattern(self):
        """测试奇偶排布计算"""
        red_balls = [1, 2, 3, 4, 5]
        result = calculate_odd_even_pattern(red_balls)
        self.assertEqual(result, "奇偶奇偶奇")


class TestZoneAnalysis(unittest.TestCase):
    """分区分析测试"""
    
    def test_calculate_zone_distribution(self):
        """测试分区分布计算"""
        # 测试每个区间一个球
        red_balls = [3, 8, 13, 18, 23]  # 区1,区2,区3,区4,区5
        result = calculate_zone_distribution(red_balls)
        expected = [1, 1, 1, 1, 1, 0, 0]
        self.assertEqual(result, expected)
    
    def test_format_zone_ratio(self):
        """测试分区比例格式化"""
        zones = [1, 0, 2, 1, 1, 0, 0]
        result = format_zone_ratio(zones)
        self.assertEqual(result, "1:0:2:1:1:0:0")
    
    def test_get_zero_zones_pattern(self):
        """测试零分区模式"""
        zones = [1, 0, 2, 1, 0, 1, 0]
        count, positions = get_zero_zones_pattern(zones)
        self.assertEqual(count, 3)
        self.assertEqual(positions, [1, 4, 6])


class TestBaseInfo(unittest.TestCase):
    """基础信息测试"""
    
    def test_extract_basic_info(self):
        """测试基础信息提取"""
        # 创建测试数据
        data = pd.DataFrame({
            '期号': [7001, 7002],
            '开奖日期': [datetime(2007, 5, 30), datetime(2007, 6, 2)],
            '红球1': [22, 15],
            '红球2': [24, 22],
            '红球3': [29, 31],
            '红球4': [31, 34],
            '红球5': [35, 35],
            '蓝球1': [4, 5],
            '蓝球2': [11, 12]
        })
        
        result = extract_basic_info(data)
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]['期号'], '7001')
        self.assertEqual(result[0]['日期'], '2007-05-30')
        self.assertEqual(result[0]['红球'], '22,24,29,31,35')
        self.assertEqual(result[0]['蓝球'], '04,11')


if __name__ == '__main__':
    unittest.main()
