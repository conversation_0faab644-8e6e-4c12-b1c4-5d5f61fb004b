#!/usr/bin/env python3
"""
优化参数预测器
应用自动参数优化结果的增强预测系统
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from enhanced_multidimensional_predictor import EnhancedMultidimensionalPredictor, EnhancedMultidimensionalTester


class OptimizedPredictor(EnhancedMultidimensionalPredictor):
    """应用最优参数的预测器"""
    
    def __init__(self, optimization_results_file: str = 'simple_optimization_results.json', seed: int = 42):
        super().__init__(seed=seed)
        
        # 加载优化结果
        self.load_optimized_parameters(optimization_results_file)
        
    def load_optimized_parameters(self, results_file: str):
        """加载优化参数"""
        try:
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            best_params = results['best_params']
            best_score = results['best_score']
            
            print(f"加载最优参数，优化分数: {best_score:.4f}")
            
            # 应用最优参数
            self.apply_optimized_parameters(best_params)
            
        except FileNotFoundError:
            print(f"优化结果文件 {results_file} 不存在，使用默认参数")
        except Exception as e:
            print(f"加载优化参数时出错: {e}，使用默认参数")
    
    def apply_optimized_parameters(self, params: Dict[str, Any]):
        """应用优化参数"""
        # 更新维度权重
        self.dimension_weights = {
            'zone_heat': params.get('zone_heat_weight', 0.25),
            'zero_slope': params.get('zero_slope_weight', 0.15),
            'position_zero': params.get('position_zero_weight', 0.15),
            'missing_analysis': params.get('missing_analysis_weight', 0.20),
            'adjacent_pattern': params.get('adjacent_pattern_weight', 0.10),
            'hot_cold_balance': params.get('hot_cold_balance_weight', 0.15)
        }
        
        # 更新分析窗口
        self.analysis_windows = {
            'short_term': params.get('short_term_window', 10),
            'medium_term': params.get('medium_term_window', 30),
            'long_term': params.get('long_term_window', 100)
        }
        
        # 更新其他参数
        if hasattr(self.base_predictor, 'exclude_recent_periods'):
            self.base_predictor.exclude_recent_periods = params.get('exclude_recent_periods', 20)
        
        print("已应用优化参数:")
        print(f"  维度权重: {self.dimension_weights}")
        print(f"  分析窗口: {self.analysis_windows}")


class OptimizedTester(EnhancedMultidimensionalTester):
    """优化参数测试器"""
    
    def __init__(self, optimization_results_file: str = 'simple_optimization_results.json'):
        # 使用优化参数创建预测器
        self.predictor = OptimizedPredictor(optimization_results_file)
    
    def run_optimized_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 200):
        """运行优化参数测试"""
        print("=" * 80)
        print("优化参数预测系统测试")
        print("=" * 80)
        
        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]
        
        print(f"训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")
        print(f"分析维度: {len(self.predictor.dimension_weights)}个")
        print(f"优化后权重: {self.predictor.dimension_weights}")
        
        print(f"\n预测结果:")
        print("-" * 80)
        
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        position_matches = [0] * 7
        enhancement_scores = []
        confidence_scores = []
        
        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]
            
            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)
            
            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")
            
            # 获取多维度分析摘要
            analysis_summary = self.predictor.get_analysis_summary(recent_history)
            
            # 生成优化预测
            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)
            
            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')
                
                # 检查完全匹配
                if match_count == 7:
                    perfect_matches += 1
                    break
                
                if j == 0:  # 统计第一个预测的其他指标
                    total_matches += match_count
                    confidence_scores.append(confidence)
                    
                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1
                    
                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1
                    
                    # 记录增强得分
                    enhancement_scores.append(info.get('enhancement_score', 0))
                
                # 显示预测信息
                enhancement_info = ""
                if info.get('multidimensional_enhancement'):
                    enhancement_score = info.get('enhancement_score', 0)
                    original_conf = info.get('original_confidence', confidence)
                    enhancement_info = f"  增强:{enhancement_score:.3f}(原:{original_conf:.3f})"
                
                print(f"优化预测：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}{enhancement_info}")
            
            # 显示关键分析指标
            print(f"  区域热度: {[f'{v:.2f}' for v in analysis_summary.get('zone_heat_analysis', {}).values()]}")
            print(f"  0斜率趋势: {analysis_summary.get('zero_slope_trend', 0):.3f}")
            print(f"  遗漏分析: {[f'{v:.2f}' for v in analysis_summary.get('missing_analysis', {}).values()]}")
            print()
        
        # 统计结果
        print(f"=" * 80)
        print("优化参数预测效果分析")
        print("=" * 80)
        
        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        perfect_accuracy = perfect_matches / test_periods
        avg_enhancement = np.mean(enhancement_scores) if enhancement_scores else 0
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0
        
        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        print(f"  完全匹配率(7/7): {perfect_accuracy:.1%} ({perfect_matches}/{test_periods}期)")
        print(f"  平均增强得分: {avg_enhancement:.3f}")
        print(f"  平均置信度: {avg_confidence:.3f}")
        print(f"  置信度稳定性: {confidence_stability:.3f}")
        
        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")
        
        print(f"\n优化效果:")
        print(f"  维度权重优化: {self.predictor.dimension_weights}")
        print(f"  分析窗口优化: {self.predictor.analysis_windows}")
        
        # 计算综合优化分数
        optimization_score = (avg_accuracy * 0.4 + zero_accuracy * 0.3 + 
                            perfect_accuracy * 0.2 + confidence_stability * 0.1)
        print(f"  综合优化分数: {optimization_score:.4f}")
        
        return {
            'position_accuracy': avg_accuracy,
            'zero_count_accuracy': zero_accuracy,
            'perfect_match_rate': perfect_accuracy,
            'enhancement_score': avg_enhancement,
            'confidence': avg_confidence,
            'confidence_stability': confidence_stability,
            'optimization_score': optimization_score
        }


def compare_with_original():
    """与原始系统对比"""
    print("=" * 80)
    print("优化前后系统对比")
    print("=" * 80)
    
    # 加载数据
    db_manager = SQLiteManager()
    df = db_manager.load_results(LotteryType.DLT)
    data = df.to_dict('records')
    
    # 测试优化系统
    print("测试优化参数系统:")
    optimized_tester = OptimizedTester()
    optimized_results = optimized_tester.run_optimized_test(data, test_periods=20)
    
    print("\n" + "=" * 80)
    print("对比总结")
    print("=" * 80)
    print("优化参数系统表现:")
    for metric, value in optimized_results.items():
        if isinstance(value, float):
            print(f"  {metric}: {value:.4f}")
        else:
            print(f"  {metric}: {value}")


def main():
    """主函数"""
    try:
        compare_with_original()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
