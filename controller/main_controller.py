"""
主控制器 - 协调模型和视图之间的交互
"""
import pandas as pd
from typing import Optional

from model.data_model import LotteryDataModel
from analysis.analyzer import LotteryAnalyzer
from config.lottery_config import LotteryType, LotteryConfig


class MainController:
    """主控制器类"""

    def __init__(self):
        self.data_model = LotteryDataModel()
        self.analyzer = LotteryAnalyzer()

    def has_existing_data(self) -> bool:
        """检查是否有现有的分析数据"""
        return self.data_model.has_database_data()

    def get_existing_data(self) -> Optional[pd.DataFrame]:
        """获取现有的分析数据"""
        return self.data_model.get_analyzed_data()
    
    def load_data(self, file_path: str, lottery_type: LotteryType = None) -> bool:
        """
        加载数据

        Args:
            file_path: CSV文件路径
            lottery_type: 彩票类型

        Returns:
            bool: 加载是否成功
        """
        return self.data_model.load_csv_data(file_path, lottery_type)
    
    def analyze_data(self) -> Optional[pd.DataFrame]:
        """
        分析数据

        Returns:
            Optional[pd.DataFrame]: 分析结果，失败时返回None
        """
        raw_data = self.data_model.get_raw_data()
        if raw_data is None:
            return None

        # 获取当前彩票类型
        lottery_type = self.data_model.get_lottery_type()

        # 进行数据分析
        analyzed_data = self.analyzer.analyze_data(raw_data, lottery_type)

        # 保存分析结果到数据库
        self.data_model.rebuild_database(analyzed_data)

        return analyzed_data

    def import_and_analyze_data(self, file_path: str, lottery_type: LotteryType = None) -> Optional[pd.DataFrame]:
        """
        导入数据并重新分析（用于导入按钮）

        Args:
            file_path: CSV文件路径
            lottery_type: 彩票类型

        Returns:
            Optional[pd.DataFrame]: 分析结果，失败时返回None
        """
        # 加载新的CSV数据
        success = self.load_data(file_path, lottery_type)
        if not success:
            return None

        # 重新分析数据
        return self.analyze_data()

    def set_lottery_type(self, lottery_type: LotteryType):
        """设置彩票类型"""
        self.data_model.set_lottery_type(lottery_type)

    def get_lottery_type(self) -> LotteryType:
        """获取当前彩票类型"""
        return self.data_model.get_lottery_type()

    def switch_lottery_type(self, lottery_type: LotteryType) -> Optional[pd.DataFrame]:
        """
        切换彩票类型并加载对应数据

        Args:
            lottery_type: 新的彩票类型

        Returns:
            Optional[pd.DataFrame]: 加载的数据，失败时返回None
        """
        # 设置新的彩票类型
        self.data_model.set_lottery_type(lottery_type)

        # 尝试从数据库加载对应类型的数据
        if self.data_model.load_from_database(lottery_type):
            return self.data_model.get_analyzed_data()

        return None

    def get_database_statistics(self) -> dict:
        """获取数据库统计信息"""
        return self.data_model.get_database_statistics()
    
    def get_data_count(self) -> int:
        """获取数据总数"""
        return self.data_model.get_data_count()
    
    def get_analyzed_data(self) -> Optional[pd.DataFrame]:
        """获取分析后的数据"""
        return self.data_model.get_analyzed_data()
    
    def clear_data(self):
        """清空数据"""
        self.data_model.clear_data()
