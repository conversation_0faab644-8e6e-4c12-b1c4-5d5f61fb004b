"""
分区分析模块
"""
from typing import List, Dict, Tuple
import pandas as pd
from datetime import datetime
from config.lottery_config import LotteryType, LotteryConfig


def calculate_zone_distribution(red_balls: List[int], lottery_type: LotteryType) -> List[int]:
    """
    计算分区分布
    根据彩票类型使用不同的分区规则：
    - 大乐透：1-35分为7个区间，每区5个号码
    - 双色球：1-33分为7个区间，前6区各5个，第7区3个

    Args:
        red_balls: 红球号码列表
        lottery_type: 彩票类型

    Returns:
        List[int]: 7个区间的号码数量
    """
    # 获取分区边界
    boundaries = LotteryConfig.calculate_zone_boundaries(lottery_type)
    zones = [0] * len(boundaries)

    for ball in red_balls:
        # 查找球号属于哪个区间
        for i, (start, end) in enumerate(boundaries):
            if start <= ball <= end:
                zones[i] += 1
                break

    return zones


def format_zone_ratio(zones: List[int]) -> str:
    """
    格式化分区比例
    
    Args:
        zones: 7个区间的号码数量
        
    Returns:
        str: 分区比例字符串，如"1:0:1:2:1:0:0"
    """
    return ":".join(map(str, zones))


def get_zero_zones_pattern(zones: List[int]) -> Tuple[int, List[int]]:
    """
    获取零分区模式
    
    Args:
        zones: 7个区间的号码数量
        
    Returns:
        Tuple[int, List[int]]: (零分区数量, 零分区位置列表)
    """
    zero_positions = [i for i, count in enumerate(zones) if count == 0]
    return len(zero_positions), zero_positions


def find_last_same_zone_ratio(current_index: int, current_ratio: str,
                              all_ratios: List[str], dates: List[str]) -> Tuple[str, int]:
    """
    查找上次相同分区比的日期和间隔
    
    Args:
        current_index: 当前数据索引
        current_ratio: 当前分区比
        all_ratios: 所有分区比列表
        dates: 所有日期列表
        
    Returns:
        Tuple[str, int]: (上次日期, 间隔天数)
    """
    for i in range(current_index - 1, -1, -1):
        if all_ratios[i] == current_ratio:
            last_date = dates[i]
            current_date = dates[current_index]
            
            last_dt = datetime.strptime(last_date, '%Y-%m-%d')
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            interval_days = (current_dt - last_dt).days
            
            return last_date, interval_days
    
    return "无", 0


def find_last_same_zero_zones(current_index: int, current_zero_count: int, 
                             current_zero_positions: List[int],
                             all_zero_patterns: List[Tuple[int, List[int]]], 
                             dates: List[str]) -> Tuple[str, int]:
    """
    查找上次相同零分区模式的日期和间隔
    
    Args:
        current_index: 当前数据索引
        current_zero_count: 当前零分区数量
        current_zero_positions: 当前零分区位置
        all_zero_patterns: 所有零分区模式列表
        dates: 所有日期列表
        
    Returns:
        Tuple[str, int]: (上次日期, 间隔天数)
    """
    for i in range(current_index - 1, -1, -1):
        zero_count, zero_positions = all_zero_patterns[i]
        if zero_count == current_zero_count and zero_positions == current_zero_positions:
            last_date = dates[i]
            current_date = dates[current_index]
            
            last_dt = datetime.strptime(last_date, '%Y-%m-%d')
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')
            interval_days = (current_dt - last_dt).days
            
            return last_date, interval_days
    
    return "无", 0


def analyze_zones(data: pd.DataFrame, lottery_type: LotteryType) -> List[Dict[str, any]]:
    """
    进行完整的分区分析

    Args:
        data: 原始数据DataFrame
        lottery_type: 彩票类型

    Returns:
        List[Dict]: 分区分析结果列表
    """
    results = []
    all_ratios = []
    all_zero_patterns = []
    dates = []

    # 获取彩票配置
    config = LotteryConfig.get_config(lottery_type)
    red_ball_columns = config.get('csv_columns', {}).get('red_balls', [])
    date_column = config.get('csv_columns', {}).get('date', '开奖日期')

    # 第一遍：计算所有分区数据
    for index, row in data.iterrows():
        try:
            red_balls = []
            for col in red_ball_columns:
                value = row[col]
                if pd.isna(value):
                    raise ValueError(f"空值在列 {col}")
                red_balls.append(int(float(value)))

            date_str = row[date_column].strftime('%Y-%m-%d')

            zones = calculate_zone_distribution(red_balls, lottery_type)
            zone_ratio = format_zone_ratio(zones)
            zero_count, zero_positions = get_zero_zones_pattern(zones)

            all_ratios.append(zone_ratio)
            all_zero_patterns.append((zero_count, zero_positions))
            dates.append(date_str)
        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 跳过该记录")
            all_ratios.append("")  # 占位符
            all_zero_patterns.append((0, []))
            dates.append("")

    # 第二遍：查找历史相同模式
    for index, row in data.iterrows():
        try:
            red_balls = []
            for col in red_ball_columns:
                value = row[col]
                if pd.isna(value):
                    raise ValueError(f"空值在列 {col}")
                red_balls.append(int(float(value)))

            zones = calculate_zone_distribution(red_balls, lottery_type)
            zone_ratio = format_zone_ratio(zones)
            zero_count, zero_positions = get_zero_zones_pattern(zones)

            # 查找上次相同分区比
            last_ratio_date, ratio_interval = find_last_same_zone_ratio(
                index, zone_ratio, all_ratios, dates
            )

            # 查找上次相同零分区模式
            last_zero_date, zero_interval = find_last_same_zero_zones(
                index, zero_count, zero_positions, all_zero_patterns, dates
            )

            last_ratio_info = f"{last_ratio_date}({ratio_interval}天)" if last_ratio_date != "无" else "无"
            last_zero_info = f"{last_zero_date}({zero_interval}天)" if last_zero_date != "无" else "无"

            result = {
                '分区比': zone_ratio,
                '上次分区比': last_ratio_info,
                '上次0分区比': last_zero_info
            }
        except (ValueError, TypeError) as e:
            print(f"警告: 第{index+1}行数据处理失败: {e}, 使用默认值")
            result = {
                '分区比': "0:0:0:0:0:0:0",
                '上次分区比': "无",
                '上次0分区比': "无"
            }

        results.append(result)

    return results
