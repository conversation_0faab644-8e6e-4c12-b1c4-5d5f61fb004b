"""
基础信息提取模块
"""
from typing import List, Dict, Any
import pandas as pd
from config.lottery_config import LotteryType, LotteryConfig


def extract_basic_info(data: pd.DataFrame, lottery_type: LotteryType) -> List[Dict[str, Any]]:
    """
    提取基础信息：日期、期号、红球、蓝球

    Args:
        data: 原始数据DataFrame
        lottery_type: 彩票类型

    Returns:
        List[Dict]: 包含基础信息的字典列表
    """
    basic_info = []

    # 获取彩票配置
    config = LotteryConfig.get_config(lottery_type)
    csv_columns = config.get('csv_columns', {})
    issue_column = csv_columns.get('issue', '期号')
    date_column = csv_columns.get('date', '开奖日期')
    red_ball_columns = csv_columns.get('red_balls', [])
    blue_ball_columns = csv_columns.get('blue_balls', [])

    for index, row in data.iterrows():
        # 格式化红球
        red_balls = []
        for col in red_ball_columns:
            try:
                value = row[col]
                if pd.isna(value):
                    print(f"警告: 第{index+1}行的{col}列包含空值，跳过该记录")
                    break
                red_balls.append(int(float(value)))
            except (ValueError, TypeError) as e:
                print(f"警告: 第{index+1}行的{col}列数据转换失败: {value}, 跳过该记录")
                break

        # 如果红球数据不完整，跳过这条记录
        if len(red_balls) != len(red_ball_columns):
            continue

        red_ball_str = ",".join([f"{ball:02d}" for ball in red_balls])

        # 格式化蓝球
        blue_balls = []
        for col in blue_ball_columns:
            try:
                value = row[col]
                if pd.isna(value):
                    print(f"警告: 第{index+1}行的{col}列包含空值，跳过该记录")
                    break
                blue_balls.append(int(float(value)))
            except (ValueError, TypeError) as e:
                print(f"警告: 第{index+1}行的{col}列数据转换失败: {value}, 跳过该记录")
                break

        # 如果蓝球数据不完整，跳过这条记录
        if len(blue_balls) != len(blue_ball_columns):
            continue

        blue_ball_str = ",".join([f"{ball:02d}" for ball in blue_balls])

        info = {
            '日期': row[date_column].strftime('%Y-%m-%d'),
            '期号': str(row[issue_column]),
            '红球': red_ball_str,
            '蓝球': blue_ball_str,
            '红球列表': red_balls,
            '蓝球列表': blue_balls
        }
        basic_info.append(info)

    return basic_info
