"""
数据分析器 - 整合所有分析功能
"""
import pandas as pd
from typing import Dict, List, Any

from analysis.base_info import extract_basic_info
from analysis.odd_even_analysis import analyze_odd_even
from analysis.zone_analysis import analyze_zones
from config.lottery_config import LotteryType


class LotteryAnalyzer:
    """彩票数据分析器"""

    def __init__(self):
        self.analyzed_data = None

    def analyze_data(self, data: pd.DataFrame, lottery_type: LotteryType) -> pd.DataFrame:
        """
        分析彩票数据

        Args:
            data: 原始数据DataFrame
            lottery_type: 彩票类型

        Returns:
            pd.DataFrame: 分析结果DataFrame
        """
        if data is None or len(data) == 0:
            return pd.DataFrame()

        # 提取基础信息
        basic_info = extract_basic_info(data, lottery_type)

        # 奇偶分析
        odd_even_results = analyze_odd_even(data, lottery_type)

        # 分区分析
        zone_results = analyze_zones(data, lottery_type)

        # 合并所有分析结果
        results = []
        for i in range(len(basic_info)):
            result = {
                '日期': basic_info[i]['日期'],
                '期号': basic_info[i]['期号'],
                '红球': basic_info[i]['红球'],
                '蓝球': basic_info[i]['蓝球'],
                '奇偶比': odd_even_results[i]['奇偶比'],
                '奇偶排布': odd_even_results[i]['奇偶排布'],
                '上次奇偶排布': odd_even_results[i]['上次奇偶排布'],
                '分区比': zone_results[i]['分区比'],
                '上次分区比': zone_results[i]['上次分区比'],
                '上次0分区比': zone_results[i]['上次0分区比']
            }
            results.append(result)

        # 转换为DataFrame
        self.analyzed_data = pd.DataFrame(results)
        return self.analyzed_data

    def get_analyzed_data(self) -> pd.DataFrame:
        """获取分析结果数据"""
        return self.analyzed_data
