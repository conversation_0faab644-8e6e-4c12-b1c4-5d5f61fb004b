#!/usr/bin/env python3
"""
增强多维度预测系统
基于o3-mini深度分析建议，整合多个新维度进行预测
"""
import sys
import os
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Optional
import hashlib
from scipy import stats
from sklearn.preprocessing import MinMaxScaler

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from bias_corrected_predictor import BiascorrectedPredictor


class EnhancedMultidimensionalPredictor:
    """增强多维度预测器 - 整合多个分析维度"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        self.base_predictor = BiascorrectedPredictor(seed=seed)
        self.scaler = MinMaxScaler()
        
        # 维度权重配置（基于o3-mini建议）
        self.dimension_weights = {
            'zone_heat': 0.25,           # 区域热度分析
            'zero_slope': 0.15,          # 0的斜率分析
            'position_zero': 0.15,       # 相同位置0分析
            'missing_analysis': 0.20,    # 历史遗漏分析
            'adjacent_pattern': 0.10,    # 相邻区域联合模式
            'hot_cold_balance': 0.15     # 热冷平衡分析
        }
        
        # 分析窗口配置
        self.analysis_windows = {
            'short_term': 10,    # 短期窗口
            'medium_term': 30,   # 中期窗口
            'long_term': 100     # 长期窗口
        }
        
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def analyze_zone_heat(self, recent_data: List[Dict]) -> Dict[int, float]:
        """分析区域热度"""
        zone_heat_scores = {}
        
        for window_name, window_size in self.analysis_windows.items():
            window_data = recent_data[-window_size:] if len(recent_data) >= window_size else recent_data
            zone_counts = [0] * 7
            
            for record in window_data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                for i, count in enumerate(zone_dist):
                    zone_counts[i] += count
            
            # 计算热度分数（归一化）
            total_balls = sum(zone_counts)
            if total_balls > 0:
                for i in range(7):
                    heat_score = zone_counts[i] / total_balls
                    if i not in zone_heat_scores:
                        zone_heat_scores[i] = 0
                    
                    # 根据窗口类型加权
                    weight = {'short_term': 0.5, 'medium_term': 0.3, 'long_term': 0.2}[window_name]
                    zone_heat_scores[i] += heat_score * weight
        
        return zone_heat_scores
    
    def analyze_zero_slope(self, recent_data: List[Dict]) -> float:
        """分析0分区位置的斜率趋势"""
        if len(recent_data) < 5:
            return 0.0
        
        # 提取最近期数的0分区位置
        zero_positions_series = []
        for record in recent_data[-20:]:  # 分析最近20期
            zone_dist = self.parse_zone_ratio(record['分区比'])
            zero_positions = [i for i, count in enumerate(zone_dist) if count == 0]
            if zero_positions:
                avg_position = np.mean(zero_positions)
                zero_positions_series.append(avg_position)
        
        if len(zero_positions_series) < 3:
            return 0.0
        
        # 计算线性回归斜率
        x = np.arange(len(zero_positions_series))
        slope, _, r_value, _, _ = stats.linregress(x, zero_positions_series)
        
        # 返回斜率强度（考虑相关性）
        return slope * abs(r_value)
    
    def analyze_position_zero_patterns(self, recent_data: List[Dict]) -> Dict[int, float]:
        """分析各位置出现0的模式"""
        position_zero_scores = {}
        
        for pos in range(7):
            # 统计该位置最近出现0的频率
            recent_zeros = 0
            total_periods = min(len(recent_data), 50)
            
            for record in recent_data[-total_periods:]:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if pos < len(zone_dist) and zone_dist[pos] == 0:
                    recent_zeros += 1
            
            # 计算该位置的0出现概率
            zero_probability = recent_zeros / total_periods if total_periods > 0 else 0
            
            # 分析周期性（简化版）
            zero_intervals = []
            last_zero_index = -1
            
            for i, record in enumerate(recent_data[-total_periods:]):
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if pos < len(zone_dist) and zone_dist[pos] == 0:
                    if last_zero_index >= 0:
                        zero_intervals.append(i - last_zero_index)
                    last_zero_index = i
            
            # 计算周期性得分
            periodicity_score = 0
            if len(zero_intervals) > 1:
                interval_std = np.std(zero_intervals)
                interval_mean = np.mean(zero_intervals)
                # 标准差越小，周期性越强
                periodicity_score = 1 / (1 + interval_std / max(interval_mean, 1))
            
            position_zero_scores[pos] = zero_probability * 0.7 + periodicity_score * 0.3
        
        return position_zero_scores
    
    def analyze_missing_patterns(self, recent_data: List[Dict]) -> Dict[int, float]:
        """分析历史遗漏模式"""
        missing_scores = {}
        
        # 分析每个区域的遗漏情况
        for zone in range(7):
            missing_periods = 0
            total_periods = min(len(recent_data), 100)
            
            # 从最新期开始向前统计连续遗漏
            for record in reversed(recent_data[-total_periods:]):
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if zone < len(zone_dist) and zone_dist[zone] == 0:
                    missing_periods += 1
                else:
                    break
            
            # 计算历史平均遗漏期数
            all_missing_intervals = []
            current_missing = 0
            
            for record in recent_data[-total_periods:]:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if zone < len(zone_dist) and zone_dist[zone] == 0:
                    current_missing += 1
                else:
                    if current_missing > 0:
                        all_missing_intervals.append(current_missing)
                        current_missing = 0
            
            avg_missing = np.mean(all_missing_intervals) if all_missing_intervals else 5
            
            # 计算遗漏得分（当前遗漏期数相对于历史平均的比例）
            missing_ratio = missing_periods / max(avg_missing, 1)
            missing_scores[zone] = min(missing_ratio, 2.0)  # 限制最大值
        
        return missing_scores
    
    def analyze_adjacent_patterns(self, recent_data: List[Dict]) -> Dict[Tuple[int, int], float]:
        """分析相邻区域联合模式"""
        adjacent_scores = {}
        
        # 定义相邻区域对
        adjacent_pairs = [(i, i+1) for i in range(6)]
        
        for pair in adjacent_pairs:
            zone1, zone2 = pair
            
            # 统计相邻区域的联合出现模式
            both_zero = 0
            both_nonzero = 0
            mixed = 0
            total = min(len(recent_data), 50)
            
            for record in recent_data[-total:]:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if len(zone_dist) > max(zone1, zone2):
                    z1_zero = zone_dist[zone1] == 0
                    z2_zero = zone_dist[zone2] == 0
                    
                    if z1_zero and z2_zero:
                        both_zero += 1
                    elif not z1_zero and not z2_zero:
                        both_nonzero += 1
                    else:
                        mixed += 1
            
            # 计算联合模式得分
            if total > 0:
                both_zero_ratio = both_zero / total
                both_nonzero_ratio = both_nonzero / total
                mixed_ratio = mixed / total
                
                # 相邻区域的协同性得分
                cooperation_score = max(both_zero_ratio, both_nonzero_ratio) - mixed_ratio
                adjacent_scores[pair] = max(0, cooperation_score)
        
        return adjacent_scores
    
    def analyze_hot_cold_balance(self, recent_data: List[Dict]) -> Dict[int, float]:
        """分析热冷平衡"""
        balance_scores = {}
        
        for zone in range(7):
            # 计算短期和长期的出现频率
            short_term_data = recent_data[-self.analysis_windows['short_term']:]
            long_term_data = recent_data[-self.analysis_windows['long_term']:]
            
            short_term_count = 0
            long_term_count = 0
            
            for record in short_term_data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if zone < len(zone_dist):
                    short_term_count += zone_dist[zone]
            
            for record in long_term_data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                if zone < len(zone_dist):
                    long_term_count += zone_dist[zone]
            
            # 计算频率比率
            short_freq = short_term_count / len(short_term_data) if short_term_data else 0
            long_freq = long_term_count / len(long_term_data) if long_term_data else 0
            
            # 热冷平衡得分（短期频率相对于长期频率的偏离程度）
            if long_freq > 0:
                balance_ratio = short_freq / long_freq
                # 转换为平衡得分（1表示平衡，偏离1越远表示越不平衡）
                balance_scores[zone] = 1 / (1 + abs(balance_ratio - 1))
            else:
                balance_scores[zone] = 0.5  # 默认中性得分
        
        return balance_scores

    def integrate_multidimensional_analysis(self, recent_data: List[Dict]) -> Dict[int, float]:
        """整合多维度分析结果"""
        # 执行各维度分析
        zone_heat = self.analyze_zone_heat(recent_data)
        zero_slope_score = self.analyze_zero_slope(recent_data)
        position_zero = self.analyze_position_zero_patterns(recent_data)
        missing_analysis = self.analyze_missing_patterns(recent_data)
        adjacent_patterns = self.analyze_adjacent_patterns(recent_data)
        hot_cold_balance = self.analyze_hot_cold_balance(recent_data)

        # 整合各维度得分
        integrated_scores = {}

        for zone in range(7):
            score = 0.0

            # 区域热度（权重25%）
            if zone in zone_heat:
                score += zone_heat[zone] * self.dimension_weights['zone_heat']

            # 0的斜率影响（权重15%）- 全局影响
            score += abs(zero_slope_score) * self.dimension_weights['zero_slope']

            # 位置0模式（权重15%）
            if zone in position_zero:
                score += position_zero[zone] * self.dimension_weights['position_zero']

            # 遗漏分析（权重20%）
            if zone in missing_analysis:
                score += missing_analysis[zone] * self.dimension_weights['missing_analysis']

            # 相邻模式影响（权重10%）
            adjacent_score = 0
            adjacent_count = 0
            for pair, pair_score in adjacent_patterns.items():
                if zone in pair:
                    adjacent_score += pair_score
                    adjacent_count += 1
            if adjacent_count > 0:
                score += (adjacent_score / adjacent_count) * self.dimension_weights['adjacent_pattern']

            # 热冷平衡（权重15%）
            if zone in hot_cold_balance:
                score += hot_cold_balance[zone] * self.dimension_weights['hot_cold_balance']

            integrated_scores[zone] = score

        return integrated_scores

    def predict_with_multidimensional_enhancement(self, recent_data: List[Dict], num_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """使用多维度增强的预测"""
        if not recent_data:
            return [("xx0x0x0", 0.3, {})] * num_predictions

        # 获取基础预测
        base_predictions = self.base_predictor.predict_with_bias_correction(recent_data, num_predictions)

        # 获取多维度分析结果
        multidim_scores = self.integrate_multidimensional_analysis(recent_data)

        # 增强预测结果
        enhanced_predictions = []

        for i, (pattern, confidence, info) in enumerate(base_predictions):
            # 计算多维度增强得分
            pattern_zones = self.parse_zone_ratio(':'.join([str(1 if c == 'x' else 0) for c in pattern]))

            enhancement_score = 0.0
            for zone, zone_count in enumerate(pattern_zones):
                if zone in multidim_scores:
                    # 如果该区域有球，使用正向得分；如果是0区域，使用反向得分
                    if zone_count > 0:
                        enhancement_score += multidim_scores[zone]
                    else:
                        enhancement_score += (1 - multidim_scores[zone])

            # 归一化增强得分
            enhancement_score = enhancement_score / 7

            # 融合基础置信度和多维度得分
            enhanced_confidence = confidence * 0.7 + enhancement_score * 0.3
            enhanced_confidence = min(0.95, max(0.05, enhanced_confidence))

            # 更新信息
            enhanced_info = info.copy()
            enhanced_info.update({
                'multidimensional_enhancement': True,
                'enhancement_score': enhancement_score,
                'original_confidence': confidence,
                'zone_heat_scores': {k: v for k, v in multidim_scores.items()},
                'analysis_dimensions': list(self.dimension_weights.keys())
            })

            enhanced_predictions.append((pattern, enhanced_confidence, enhanced_info))

        # 按增强后的置信度排序
        enhanced_predictions.sort(key=lambda x: x[1], reverse=True)

        return enhanced_predictions

    def get_analysis_summary(self, recent_data: List[Dict]) -> Dict:
        """获取分析摘要"""
        if not recent_data:
            return {}

        zone_heat = self.analyze_zone_heat(recent_data)
        zero_slope = self.analyze_zero_slope(recent_data)
        position_zero = self.analyze_position_zero_patterns(recent_data)
        missing_analysis = self.analyze_missing_patterns(recent_data)
        adjacent_patterns = self.analyze_adjacent_patterns(recent_data)
        hot_cold_balance = self.analyze_hot_cold_balance(recent_data)

        return {
            'zone_heat_analysis': zone_heat,
            'zero_slope_trend': zero_slope,
            'position_zero_patterns': position_zero,
            'missing_analysis': missing_analysis,
            'adjacent_patterns': adjacent_patterns,
            'hot_cold_balance': hot_cold_balance,
            'dimension_weights': self.dimension_weights,
            'analysis_windows': self.analysis_windows
        }


class EnhancedMultidimensionalTester:
    """增强多维度预测测试器"""

    def __init__(self):
        self.predictor = EnhancedMultidimensionalPredictor(seed=42)

    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7

    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])

    def run_enhanced_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 200):
        """运行增强多维度预测测试"""
        print("=" * 80)
        print("增强多维度预测系统测试")
        print("=" * 80)

        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")
        print(f"分析维度: {len(self.predictor.dimension_weights)}个")
        print(f"维度权重: {self.predictor.dimension_weights}")

        print(f"\n预测结果:")
        print("-" * 80)

        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0  # 7个位置都命中的个数
        position_matches = [0] * 7
        enhancement_scores = []

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]
            recent_history = history[-50:]  # 使用更多历史数据进行多维度分析

            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)

            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")

            # 获取多维度分析摘要
            analysis_summary = self.predictor.get_analysis_summary(recent_history)

            # 生成增强预测
            predictions = self.predictor.predict_with_multidimensional_enhancement(recent_history, num_predictions=5)

            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')

                if j == 0:  # 统计第一个预测
                    total_matches += match_count
                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1

                    # 检查是否完全匹配（7个位置都命中）
                    if match_count == 7:
                        perfect_matches += 1

                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1

                    # 记录增强得分
                    enhancement_scores.append(info.get('enhancement_score', 0))

                # 显示预测信息
                enhancement_info = ""
                if info.get('multidimensional_enhancement'):
                    enhancement_score = info.get('enhancement_score', 0)
                    original_conf = info.get('original_confidence', confidence)
                    enhancement_info = f"  增强:{enhancement_score:.3f}(原:{original_conf:.3f})"

                print(f"多维预测：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}{enhancement_info}")

            # 显示关键分析指标
            if i < 3:  # 只显示前3期的详细分析
                print(f"  区域热度: {[f'{v:.2f}' for v in analysis_summary.get('zone_heat_analysis', {}).values()]}")
                print(f"  0斜率趋势: {analysis_summary.get('zero_slope_trend', 0):.3f}")
                print(f"  遗漏分析: {[f'{v:.2f}' for v in analysis_summary.get('missing_analysis', {}).values()]}")

            print()

        # 统计结果
        print(f"=" * 80)
        print("增强多维度预测效果分析")
        print("=" * 80)

        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        perfect_accuracy = perfect_matches / test_periods
        avg_enhancement = np.mean(enhancement_scores) if enhancement_scores else 0

        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        print(f"  完全匹配率(7/7): {perfect_accuracy:.1%} ({perfect_matches}/{test_periods}期)")
        print(f"  平均增强得分: {avg_enhancement:.3f}")

        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")

        print(f"\n多维度分析:")
        for dim, weight in self.predictor.dimension_weights.items():
            print(f"  {dim}: 权重{weight:.1%}")

        print(f"\n分析窗口:")
        for window, size in self.predictor.analysis_windows.items():
            print(f"  {window}: {size}期")


def main():
    """主函数"""
    try:
        # 加载数据
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')

        if len(data) < 300:
            print("数据不足")
            return

        # 运行增强多维度预测测试
        tester = EnhancedMultidimensionalTester()
        tester.run_enhanced_test(data, test_periods=20, train_periods=200)

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
