#!/usr/bin/env python3
"""
反向思维预测器
既然正向预测命中率低，那就反向思考：
1. 预测什么不会出现，然后排除
2. 找出最不可能的模式，避开它们
3. 基于"反常识"进行预测
4. 利用人类心理偏差进行反向操作
"""
import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class ReverseThinkingPredictor:
    """反向思维预测器"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        np.random.seed(seed)
        
        # 反向思维策略权重
        self.reverse_strategies = {
            'avoid_recent': 0.3,        # 避开最近出现的模式
            'anti_pattern': 0.25,       # 反模式预测
            'chaos_theory': 0.2,        # 混沌理论：寻找看似随机中的规律
            'contrarian': 0.15,         # 逆向投资思维
            'entropy_max': 0.1          # 最大熵原理
        }
    
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def analyze_what_not_to_predict(self, data: List[Dict], window_size: int = 30) -> Dict[str, Any]:
        """分析什么不应该预测"""
        recent_data = data[-window_size:] if len(data) >= window_size else data
        
        analysis = {
            'forbidden_patterns': set(),
            'overused_positions': [],
            'saturated_zero_counts': [],
            'predictable_sequences': []
        }
        
        # 1. 收集最近频繁出现的模式（应该避免）
        recent_patterns = []
        recent_zero_counts = []
        
        for record in recent_data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            zero_count = pattern.count('0')
            
            recent_patterns.append(pattern)
            recent_zero_counts.append(zero_count)
        
        # 标记频繁模式为"禁止"
        pattern_counts = Counter(recent_patterns)
        avg_frequency = len(recent_patterns) / len(pattern_counts) if pattern_counts else 1
        
        for pattern, count in pattern_counts.items():
            if count > avg_frequency * 1.5:  # 超过平均频率1.5倍的模式
                analysis['forbidden_patterns'].add(pattern)
        
        # 2. 找出过度使用的位置
        position_zero_counts = [0] * 7
        for pattern in recent_patterns:
            for i, char in enumerate(pattern):
                if char == '0':
                    position_zero_counts[i] += 1
        
        avg_zero_per_position = sum(position_zero_counts) / 7
        for i, count in enumerate(position_zero_counts):
            if count > avg_zero_per_position * 1.3:
                analysis['overused_positions'].append(i)
        
        # 3. 找出饱和的0分区数量
        zero_count_freq = Counter(recent_zero_counts)
        avg_zero_freq = len(recent_zero_counts) / len(zero_count_freq) if zero_count_freq else 1
        
        for zero_count, freq in zero_count_freq.items():
            if freq > avg_zero_freq * 1.4:
                analysis['saturated_zero_counts'].append(zero_count)
        
        # 4. 检测可预测的序列（应该打破）
        for i in range(len(recent_patterns) - 2):
            if recent_patterns[i] == recent_patterns[i+1] == recent_patterns[i+2]:
                analysis['predictable_sequences'].append(recent_patterns[i])
        
        return analysis
    
    def generate_anti_patterns(self, forbidden_analysis: Dict[str, Any]) -> List[str]:
        """生成反模式"""
        anti_patterns = []
        
        # 策略1：生成与禁止模式完全相反的模式
        for forbidden_pattern in forbidden_analysis['forbidden_patterns']:
            anti_pattern = ''
            for char in forbidden_pattern:
                anti_pattern += 'x' if char == '0' else '0'
            anti_patterns.append(anti_pattern)
        
        # 策略2：避开过度使用的位置
        overused_positions = set(forbidden_analysis['overused_positions'])
        for _ in range(3):
            pattern = ['x'] * 7
            # 在非过度使用的位置放置0
            available_positions = [i for i in range(7) if i not in overused_positions]
            if len(available_positions) >= 2:
                zero_count = np.random.choice([2, 3, 4], p=[0.3, 0.4, 0.3])
                zero_count = min(zero_count, len(available_positions))
                zero_positions = np.random.choice(available_positions, size=zero_count, replace=False)
                for pos in zero_positions:
                    pattern[pos] = '0'
            anti_patterns.append(''.join(pattern))
        
        # 策略3：使用非饱和的0分区数量
        saturated_counts = set(forbidden_analysis['saturated_zero_counts'])
        all_possible_counts = {2, 3, 4, 5}
        preferred_counts = list(all_possible_counts - saturated_counts)
        
        if preferred_counts:
            for _ in range(2):
                zero_count = np.random.choice(preferred_counts)
                pattern = ['x'] * 7
                zero_positions = np.random.choice(7, size=zero_count, replace=False)
                for pos in zero_positions:
                    pattern[pos] = '0'
                anti_patterns.append(''.join(pattern))
        
        return anti_patterns
    
    def chaos_theory_prediction(self, data: List[Dict]) -> List[str]:
        """混沌理论预测：在看似随机中寻找微妙规律"""
        if len(data) < 20:
            return []
        
        chaos_patterns = []
        
        # 寻找微妙的周期性
        recent_data = data[-50:] if len(data) >= 50 else data
        patterns = []
        
        for record in recent_data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            patterns.append(pattern)
        
        # 检测隐藏的周期
        for period in [3, 5, 7, 11, 13]:  # 使用质数周期
            if len(patterns) >= period * 2:
                # 检查是否存在周期性模式
                period_patterns = []
                for i in range(0, len(patterns) - period, period):
                    if i + period < len(patterns):
                        period_patterns.append(patterns[i + period])
                
                if period_patterns:
                    # 基于周期性预测下一个
                    last_in_cycle = patterns[-(len(patterns) % period)] if len(patterns) % period != 0 else patterns[-period]
                    chaos_patterns.append(last_in_cycle)
        
        # 蝴蝶效应：微小变化产生大影响
        if patterns:
            last_pattern = patterns[-1]
            # 对最后一个模式进行微调
            butterfly_pattern = list(last_pattern)
            # 随机改变1-2个位置
            change_count = np.random.choice([1, 2])
            change_positions = np.random.choice(7, size=change_count, replace=False)
            
            for pos in change_positions:
                butterfly_pattern[pos] = '0' if butterfly_pattern[pos] == 'x' else 'x'
            
            chaos_patterns.append(''.join(butterfly_pattern))
        
        return chaos_patterns
    
    def contrarian_prediction(self, data: List[Dict]) -> List[str]:
        """逆向投资思维：做与大众期望相反的事"""
        if len(data) < 10:
            return []
        
        contrarian_patterns = []
        
        # 分析"常识"预测会是什么
        recent_data = data[-20:] if len(data) >= 20 else data
        
        # 常识1：连续出现相同模式后，下次应该不同
        last_patterns = []
        for record in recent_data[-3:]:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            last_patterns.append(pattern)
        
        if len(last_patterns) >= 2 and last_patterns[-1] == last_patterns[-2]:
            # 常识说应该变化，我们反其道而行之
            contrarian_patterns.append(last_patterns[-1])
        
        # 常识2：0分区数量应该在3-4之间
        # 我们偏向选择2或5
        for target_zeros in [2, 5]:
            pattern = ['x'] * 7
            if target_zeros <= 7:
                zero_positions = np.random.choice(7, size=target_zeros, replace=False)
                for pos in zero_positions:
                    pattern[pos] = '0'
                contrarian_patterns.append(''.join(pattern))
        
        # 常识3：避免极端模式
        # 我们故意选择极端模式
        extreme_patterns = [
            '0000xxx',  # 4个连续0
            'xxx0000',  # 4个连续0在后
            '0x0x0x0',  # 交替模式
            'x0x0x0x'   # 反交替模式
        ]
        contrarian_patterns.extend(extreme_patterns)
        
        return contrarian_patterns
    
    def maximum_entropy_prediction(self, data: List[Dict]) -> List[str]:
        """最大熵原理：选择信息量最大的模式"""
        if len(data) < 30:
            return []
        
        entropy_patterns = []
        
        # 计算历史模式的信息熵
        recent_data = data[-100:] if len(data) >= 100 else data
        all_patterns = []
        
        for record in recent_data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            all_patterns.append(pattern)
        
        pattern_counts = Counter(all_patterns)
        total_patterns = len(all_patterns)
        
        # 计算每个模式的概率和信息量
        pattern_info = {}
        for pattern, count in pattern_counts.items():
            prob = count / total_patterns
            info = -np.log2(prob) if prob > 0 else 0
            pattern_info[pattern] = info
        
        # 生成高信息量的新模式
        # 选择历史上很少出现的模式类型
        rare_zero_counts = []
        zero_count_freq = Counter([p.count('0') for p in all_patterns])
        total_count = sum(zero_count_freq.values())
        
        for zero_count in [2, 3, 4, 5]:
            freq = zero_count_freq.get(zero_count, 0)
            if freq < total_count * 0.15:  # 出现频率低于15%
                rare_zero_counts.append(zero_count)
        
        # 生成稀有0分区数量的模式
        for zero_count in rare_zero_counts[:2]:
            pattern = ['x'] * 7
            zero_positions = np.random.choice(7, size=zero_count, replace=False)
            for pos in zero_positions:
                pattern[pos] = '0'
            entropy_patterns.append(''.join(pattern))
        
        return entropy_patterns
    
    def predict_reverse_thinking(self, data: List[Dict], n_predictions: int = 5) -> List[Tuple[str, float, Dict]]:
        """反向思维预测"""
        if len(data) < 10:
            return [("xx0x0x0", 0.3, {})] * n_predictions
        
        # 1. 分析什么不应该预测
        forbidden_analysis = self.analyze_what_not_to_predict(data)
        
        # 2. 收集各种反向策略的预测
        all_reverse_predictions = []
        
        # 反模式预测
        anti_patterns = self.generate_anti_patterns(forbidden_analysis)
        for pattern in anti_patterns:
            all_reverse_predictions.append((pattern, 'anti_pattern'))
        
        # 混沌理论预测
        chaos_patterns = self.chaos_theory_prediction(data)
        for pattern in chaos_patterns:
            all_reverse_predictions.append((pattern, 'chaos_theory'))
        
        # 逆向思维预测
        contrarian_patterns = self.contrarian_prediction(data)
        for pattern in contrarian_patterns:
            all_reverse_predictions.append((pattern, 'contrarian'))
        
        # 最大熵预测
        entropy_patterns = self.maximum_entropy_prediction(data)
        for pattern in entropy_patterns:
            all_reverse_predictions.append((pattern, 'entropy_max'))
        
        # 3. 过滤和评分
        final_predictions = []
        seen_patterns = set()
        
        for pattern, strategy in all_reverse_predictions:
            if pattern not in seen_patterns and self._is_valid_pattern(pattern):
                # 计算反向置信度
                confidence = self._calculate_reverse_confidence(pattern, forbidden_analysis, strategy)
                
                info = {
                    'reverse_thinking': True,
                    'strategy': strategy,
                    'forbidden_analysis': {
                        'avoided_patterns': len(forbidden_analysis['forbidden_patterns']),
                        'avoided_positions': len(forbidden_analysis['overused_positions']),
                        'avoided_zero_counts': len(forbidden_analysis['saturated_zero_counts'])
                    }
                }
                
                final_predictions.append((pattern, confidence, info))
                seen_patterns.add(pattern)
        
        # 4. 按置信度排序并返回
        final_predictions.sort(key=lambda x: x[1], reverse=True)
        
        # 如果预测不足，生成随机反向预测
        while len(final_predictions) < n_predictions:
            random_pattern = self._generate_random_reverse_pattern(forbidden_analysis)
            if random_pattern not in seen_patterns:
                confidence = 0.2 + np.random.random() * 0.3
                info = {'reverse_thinking': True, 'strategy': 'random_reverse'}
                final_predictions.append((random_pattern, confidence, info))
                seen_patterns.add(random_pattern)
        
        return final_predictions[:n_predictions]
    
    def _is_valid_pattern(self, pattern: str) -> bool:
        """检查模式是否有效"""
        if len(pattern) != 7:
            return False
        
        zero_count = pattern.count('0')
        return 1 <= zero_count <= 6  # 至少1个0，最多6个0
    
    def _calculate_reverse_confidence(self, pattern: str, forbidden_analysis: Dict, strategy: str) -> float:
        """计算反向置信度"""
        base_confidence = self.reverse_strategies.get(strategy, 0.2)
        
        # 奖励避开禁止模式
        if pattern not in forbidden_analysis['forbidden_patterns']:
            base_confidence += 0.1
        
        # 奖励避开过度使用的位置
        overused_positions = set(forbidden_analysis['overused_positions'])
        zero_positions = [i for i, char in enumerate(pattern) if char == '0']
        avoided_overused = len([pos for pos in zero_positions if pos not in overused_positions])
        base_confidence += avoided_overused * 0.05
        
        # 奖励使用非饱和的0分区数量
        zero_count = pattern.count('0')
        if zero_count not in forbidden_analysis['saturated_zero_counts']:
            base_confidence += 0.15
        
        # 添加随机性
        base_confidence += np.random.normal(0, 0.05)
        
        return max(0.1, min(0.9, base_confidence))
    
    def _generate_random_reverse_pattern(self, forbidden_analysis: Dict) -> str:
        """生成随机反向模式"""
        # 选择非饱和的0分区数量
        saturated_counts = set(forbidden_analysis['saturated_zero_counts'])
        all_counts = {2, 3, 4}
        preferred_counts = list(all_counts - saturated_counts)
        
        if preferred_counts:
            zero_count = np.random.choice(preferred_counts)
        else:
            zero_count = np.random.choice([2, 3, 4])
        
        # 避开过度使用的位置
        overused_positions = set(forbidden_analysis['overused_positions'])
        available_positions = [i for i in range(7) if i not in overused_positions]
        
        if len(available_positions) >= zero_count:
            zero_positions = np.random.choice(available_positions, size=zero_count, replace=False)
        else:
            zero_positions = np.random.choice(7, size=zero_count, replace=False)
        
        pattern = ['x'] * 7
        for pos in zero_positions:
            pattern[pos] = '0'
        
        return ''.join(pattern)


class ReverseThinkingTester:
    """反向思维测试器"""

    def __init__(self):
        self.predictor = ReverseThinkingPredictor(seed=42)

    def run_reverse_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 200):
        """运行反向思维测试"""
        print("=" * 80)
        print("反向思维预测系统测试")
        print("=" * 80)
        print("核心理念：既然正向预测命中率低，那就反向思考！")
        print("策略：预测什么不会出现，避开常见模式，寻找反常规规律")

        # 准备数据
        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"\n训练样本: {train_periods}期")
        print(f"测试样本: {test_periods}期")

        print(f"\n反向思维策略权重:")
        for strategy, weight in self.predictor.reverse_strategies.items():
            strategy_names = {
                'avoid_recent': '避开最近模式',
                'anti_pattern': '反模式预测',
                'chaos_theory': '混沌理论',
                'contrarian': '逆向思维',
                'entropy_max': '最大熵原理'
            }
            print(f"  {strategy_names.get(strategy, strategy)}: {weight:.1%}")

        print(f"\n预测结果:")
        print("-" * 80)

        # 统计变量
        total_matches = 0
        zero_count_matches = 0
        perfect_matches = 0
        position_matches = [0] * 7
        confidence_scores = []
        strategy_usage = defaultdict(int)
        reverse_success_rate = []

        for i, test_record in enumerate(test_data):
            history = train_data + test_data[:i]

            actual_zone_dist = self.predictor.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.predictor.get_zero_pattern(actual_zone_dist)

            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")

            # 分析禁止预测的内容
            forbidden_analysis = self.predictor.analyze_what_not_to_predict(history)
            print(f"  反向分析：禁止{len(forbidden_analysis['forbidden_patterns'])}个模式，"
                  f"避开{len(forbidden_analysis['overused_positions'])}个位置，"
                  f"排除{len(forbidden_analysis['saturated_zero_counts'])}个0分区数")

            # 生成反向预测
            predictions = self.predictor.predict_reverse_thinking(history, n_predictions=5)

            # 检查反向策略是否成功（实际结果是否真的避开了禁止内容）
            reverse_success = actual_pattern not in forbidden_analysis['forbidden_patterns']
            reverse_success_rate.append(reverse_success)

            for j, (predicted_pattern, confidence, info) in enumerate(predictions):
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"
                predicted_zero_count = predicted_pattern.count('0')

                # 检查完全匹配
                if match_count == 7:
                    perfect_matches += 1
                    break

                if j == 0:  # 统计第一个预测
                    total_matches += match_count
                    confidence_scores.append(confidence)

                    if predicted_pattern.count('0') == actual_pattern.count('0'):
                        zero_count_matches += 1

                    # 统计各位置匹配情况
                    for pos in range(7):
                        if predicted_pattern[pos] == actual_pattern[pos]:
                            position_matches[pos] += 1

                    # 统计策略使用情况
                    strategy = info.get('strategy', 'unknown')
                    strategy_usage[strategy] += 1

                # 显示预测信息
                strategy_name = {
                    'anti_pattern': '反模式',
                    'chaos_theory': '混沌',
                    'contrarian': '逆向',
                    'entropy_max': '最大熵',
                    'random_reverse': '随机反向'
                }.get(info.get('strategy', ''), info.get('strategy', ''))

                forbidden_info = info.get('forbidden_analysis', {})
                avoid_info = f"避开{forbidden_info.get('avoided_patterns', 0)}模式"

                print(f"反向预测：{predicted_pattern}({predicted_zero_count}个0)  匹配度：{match_ratio}  置信度：{confidence:.3f}  策略:{strategy_name}  {avoid_info}")

            success_indicator = "✓反向成功" if reverse_success else "✗反向失败"
            print(f"  {success_indicator}：实际结果{'避开了' if reverse_success else '落入了'}禁止模式")
            print()

        # 统计结果
        print(f"=" * 80)
        print("反向思维预测效果分析")
        print("=" * 80)

        avg_accuracy = total_matches / (test_periods * 7)
        zero_accuracy = zero_count_matches / test_periods
        perfect_accuracy = perfect_matches / test_periods
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        confidence_stability = 1 - np.std(confidence_scores) if confidence_scores else 0
        reverse_success_ratio = np.mean(reverse_success_rate) if reverse_success_rate else 0

        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_accuracy:.1%}")
        print(f"  完全匹配率(7/7): {perfect_accuracy:.1%} ({perfect_matches}/{test_periods}期)")
        print(f"  平均置信度: {avg_confidence:.3f}")
        print(f"  置信度稳定性: {confidence_stability:.3f}")
        print(f"  反向策略成功率: {reverse_success_ratio:.1%}")

        print(f"\n各位置准确率:")
        for pos in range(7):
            pos_accuracy = position_matches[pos] / test_periods
            print(f"  位置{pos+1}: {pos_accuracy:.1%}")

        print(f"\n反向策略使用统计:")
        total_usage = sum(strategy_usage.values())
        for strategy, count in strategy_usage.items():
            strategy_names = {
                'anti_pattern': '反模式预测',
                'chaos_theory': '混沌理论',
                'contrarian': '逆向思维',
                'entropy_max': '最大熵原理',
                'random_reverse': '随机反向'
            }
            percentage = count / total_usage * 100 if total_usage > 0 else 0
            print(f"  {strategy_names.get(strategy, strategy)}: {count}次 ({percentage:.1f}%)")

        print(f"\n反向思维特色:")
        print(f"  ✓ 主动避开频繁模式")
        print(f"  ✓ 反常识预测策略")
        print(f"  ✓ 混沌理论应用")
        print(f"  ✓ 最大熵信息量优化")
        print(f"  ✓ 逆向投资思维")

        # 计算反向思维评分
        reverse_score = (avg_accuracy * 0.3 + zero_accuracy * 0.2 +
                        perfect_accuracy * 0.2 + reverse_success_ratio * 0.3)
        print(f"\n反向思维评分: {reverse_score:.4f}")

        return {
            'position_accuracy': avg_accuracy,
            'zero_count_accuracy': zero_accuracy,
            'perfect_match_rate': perfect_accuracy,
            'confidence': avg_confidence,
            'confidence_stability': confidence_stability,
            'reverse_success_rate': reverse_success_ratio,
            'reverse_score': reverse_score,
            'strategy_distribution': dict(strategy_usage)
        }


def main():
    """主函数"""
    try:
        # 加载数据
        print("加载历史数据...")
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')

        if len(data) < 300:
            print("数据不足，需要至少300期数据")
            return

        print(f"已加载 {len(data)} 期数据")

        # 运行反向思维预测测试
        tester = ReverseThinkingTester()
        results = tester.run_reverse_test(data, test_periods=20, train_periods=200)

        print("\n反向思维预测系统测试完成！")
        print("\n与传统正向预测对比:")
        print("- 增强多维度系统: 54.3%位置准确率, 5%完全匹配率")
        print("- 高级集成系统: 53.6%位置准确率, 0%完全匹配率")
        print("- 激进优化系统: 44.3%位置准确率, 5%完全匹配率")
        print(f"- 反向思维系统: {results['position_accuracy']:.1%}位置准确率, {results['perfect_match_rate']:.1%}完全匹配率")
        print(f"- 反向策略成功率: {results['reverse_success_rate']:.1%}")

        print("\n反向思维的价值:")
        print("✓ 跳出传统预测思维框架")
        print("✓ 主动避开常见错误模式")
        print("✓ 利用人类心理偏差进行反向操作")
        print("✓ 在高随机性环境中寻找反常规机会")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
