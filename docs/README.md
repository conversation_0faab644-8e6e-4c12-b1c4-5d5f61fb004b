# 大乐透数据分析系统

一个基于Python和tkinter的大乐透历史数据分析应用程序，提供图形用户界面进行数据导入和分析结果展示。专为Python 3.13和Miniconda环境优化。

## 功能特性

- **数据导入**: 支持CSV格式的大乐透历史数据导入
- **奇偶分析**: 计算红球奇偶比例和排布模式，并查找历史相同模式
- **分区分析**: 将1-35红球分为7个区间进行分布分析
- **零分区分析**: 专门分析分区比中为0的区间模式
- **图形界面**: 直观的表格展示分析结果
- **多线程处理**: 后台分析数据，保持界面响应

## 系统架构

采用MVC（Model-View-Controller）设计模式：

```
project_root/
├── model/                      # 数据模型层
│   ├── data_model.py          # 数据存储和管理
├── view/                       # 视图层
│   ├── main_window.py         # 主窗口界面
├── controller/                 # 控制器层
│   ├── main_controller.py     # 业务逻辑协调
├── analysis/                   # 数据分析模块
│   ├── base_info.py           # 基础信息提取
│   ├── odd_even_analysis.py   # 奇偶分析
│   ├── zone_analysis.py       # 分区分析
│   └── analyzer.py            # 分析器整合
├── tests/                      # 单元测试
└── main.py                     # 应用程序入口
```

## 安装和运行

### 环境要求

- Python 3.13+ (推荐)
- Miniconda/Anaconda
- tkinter (Python内置)
- pandas
- numpy

### 快速启动

**推荐方式 - 使用tkinter启动脚本:**
```bash
python run_tkinter.py
```

**传统方式:**
```bash
# 安装依赖
conda install pandas numpy python-dateutil tk

# 或使用pip
pip install -r requirements.txt

# 运行应用
python main.py
```

**使用Conda环境:**
```bash
# 创建专用环境
conda env create -f environment.yml
conda activate dlt-analysis
python main.py
```

## 数据格式

CSV文件应包含以下列：
- 期号: 大乐透期号
- 开奖日期: 格式为YYYY-MM-DD
- 红球1-红球5: 5个红球号码
- 蓝球1-蓝球2: 2个蓝球号码

示例：
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
7001,2007-05-30,22,24,29,31,35,04,11
7002,2007-06-02,15,22,31,34,35,05,12
```

## 分析指标说明

### 奇偶分析
- **奇偶比**: 红球中奇数与偶数的比例（如3:2）
- **奇偶排布**: 红球奇偶数的具体排列模式（如"奇偶奇偶偶"）
- **上次奇偶排布**: 上一次出现相同奇偶排布的日期和间隔天数

### 分区分析
- **分区比**: 将1-35分为7个区间（每区5个号码）的分布比例
  - 区1: 1-5, 区2: 6-10, 区3: 11-15, 区4: 16-20
  - 区5: 21-25, 区6: 26-30, 区7: 31-35
- **上次分区比**: 上一次出现相同分区比的日期和间隔天数
- **上次0分区比**: 上一次出现相同数量和位置的0分区的日期和间隔天数

## 测试

运行单元测试：

```bash
python -m pytest tests/
```

或者：

```bash
python -m unittest tests.test_analysis
```

## 技术特点

1. **MVC架构**: 清晰的分层设计，便于维护和扩展
2. **tkinter GUI**: 使用Python内置GUI库，无需额外安装
3. **Python 3.13兼容**: 专为最新Python版本优化
4. **Miniconda支持**: 完美适配Conda环境管理
5. **多线程处理**: 数据分析在后台线程进行，保持UI响应
6. **模块化设计**: 各分析功能独立模块，便于单独测试和修改
7. **错误处理**: 完善的异常处理和用户提示
8. **性能优化**: 使用pandas进行高效数据处理

## 扩展建议

- 添加更多分析指标（如和值分析、跨度分析等）
- 支持数据导出功能
- 添加图表可视化
- 支持数据库存储
- 添加预测功能模块
