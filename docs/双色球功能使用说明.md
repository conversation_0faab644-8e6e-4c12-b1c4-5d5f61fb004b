# 双色球功能使用说明

## 🎯 功能概述

大乐透数据分析系统现已支持双色球数据分析，用户可以在同一个应用程序中分析两种不同的彩票类型。

## 🚀 快速开始

### 1. 启动应用程序
```bash
python main.py
```

### 2. 选择彩票类型
- 在界面左上角找到"彩票类型"下拉框
- 可选择：
  - **大乐透**：默认选项，已有2753条历史数据
  - **双色球**：新增支持，包含示例数据

### 3. 切换彩票类型
- 点击下拉框选择不同的彩票类型
- 系统会自动加载对应类型的历史数据
- 如果没有数据，会提示"数据库为空，请导入CSV文件进行分析"

## 📊 双色球数据格式

### CSV文件格式要求
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,红球6,蓝球1
2023001,2023-01-01,03,11,16,22,27,32,05
2023002,2023-01-03,01,07,14,19,25,31,12
```

### 数据规格
- **红球**：6个号码，范围1-33
- **蓝球**：1个号码，范围1-16
- **日期格式**：YYYY-MM-DD
- **期号格式**：年份+期数（如2023001）

## 🔧 导入双色球数据

### 步骤说明
1. **选择双色球类型**：在下拉框中选择"双色球"
2. **点击导入按钮**：点击"导入数据(重新分析)"按钮
3. **选择CSV文件**：选择符合格式要求的双色球数据文件
4. **确认重新分析**：系统会提示是否覆盖现有数据，点击"是"
5. **等待分析完成**：系统会显示进度条和分析状态

### 示例数据
系统提供了双色球示例数据文件：`data/ssq_data.csv`
包含10条示例记录，可用于测试功能。

## 📈 分析指标说明

### 双色球特有分析
- **奇偶比**：6个红球中奇数和偶数的比例（如"3:3"）
- **奇偶排布**：红球的奇偶排列模式（如"奇偶奇偶奇偶"）
- **分区比**：红球在7个分区中的分布（如"1:1:1:1:1:1:0"）

### 分区规则差异
**大乐透分区（1-35）：**
- 区1: 1-5, 区2: 6-10, 区3: 11-15, 区4: 16-20
- 区5: 21-25, 区6: 26-30, 区7: 31-35
- 每区5个号码

**双色球分区（1-33）：**
- 区1: 1-5, 区2: 6-10, 区3: 11-15, 区4: 16-20
- 区5: 21-25, 区6: 26-30, 区7: 31-33
- 前6区各5个号码，第7区3个号码

## ⚠️ 注意事项

### 数据质量要求
- **无空值**：所有红球和蓝球列不能有空值
- **数据类型**：所有号码必须是有效整数
- **范围检查**：红球1-33，蓝球1-16
- **格式一致**：日期和期号格式必须统一

### 错误处理
系统已内置强大的错误处理机制：
- 自动跳过包含空值的记录
- 显示数据清理统计信息
- 为无效数据提供默认值
- 记录详细的错误日志

### 性能建议
- **大数据集**：建议分批导入大量数据
- **内存使用**：系统会自动优化内存使用
- **分析速度**：双色球6个红球比大乐透5个红球分析稍慢

## 🔍 故障排除

### 常见问题

**Q: 导入时提示"cannot convert float NaN to integer"**
A: 数据文件中包含空值，请检查CSV文件确保所有必要列都有数据。

**Q: 切换彩票类型后没有数据**
A: 这是正常的，不同彩票类型的数据是分别存储的，需要分别导入。

**Q: 分析结果显示"无效"或"0:0"**
A: 表示该行数据有问题被系统跳过，检查原始数据质量。

**Q: 双色球分区比显示异常**
A: 确认红球号码在1-33范围内，超出范围的号码会被忽略。

### 技术支持
如遇到其他问题，请检查：
1. CSV文件格式是否正确
2. 数据是否包含空值或非法字符
3. 文件编码是否为UTF-8
4. 控制台是否有详细错误信息

## 📝 更新日志

### v2.0 - 双色球支持
- ✅ 新增双色球数据类型支持
- ✅ 界面添加彩票类型选择功能
- ✅ 实现多彩票类型数据库存储
- ✅ 优化分区分析算法适配不同规则
- ✅ 强化数据验证和错误处理
- ✅ 自动数据库迁移功能

### 兼容性
- 完全向后兼容现有大乐透数据
- 自动升级数据库结构
- 保持原有功能不变
