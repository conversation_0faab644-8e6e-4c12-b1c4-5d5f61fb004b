#!/usr/bin/env python3
"""
分段马尔科夫链实现 - 按照2+2+3格式拆分分区
将7个分区拆分为3个子系统分别建模，降低状态空间复杂度
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class SegmentedMarkovChain:
    """分段马尔科夫链预测器"""
    
    def __init__(self, order: int = 1):
        """
        初始化分段马尔科夫链
        分区拆分：[0,1] + [2,3] + [4,5,6] = 2+2+3
        """
        self.order = order
        self.segments = {
            'segment_1': [0, 1],      # 前2个分区
            'segment_2': [2, 3],      # 中2个分区  
            'segment_3': [4, 5, 6]    # 后3个分区
        }
        
        # 每个分段的马尔科夫链
        self.markov_chains = {}
        for segment_name in self.segments:
            self.markov_chains[segment_name] = {
                'transition_matrix': defaultdict(Counter),
                'state_counts': Counter(),
                'transition_probabilities': {},
                'states': set()
            }
    
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_segment_pattern(self, zone_distribution: List[int], segment_positions: List[int]) -> str:
        """获取分段的0分区模式"""
        pattern = []
        for pos in segment_positions:
            if pos < len(zone_distribution):
                pattern.append('0' if zone_distribution[pos] == 0 else 'x')
            else:
                pattern.append('x')
        return ''.join(pattern)
    
    def create_segment_sequences(self, data: List[Dict]) -> Dict[str, List[str]]:
        """为每个分段创建状态序列"""
        segment_sequences = {segment: [] for segment in self.segments}
        
        for record in data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            
            for segment_name, positions in self.segments.items():
                segment_pattern = self.get_segment_pattern(zone_dist, positions)
                segment_sequences[segment_name].append(segment_pattern)
        
        return segment_sequences
    
    def train_segment_markov_chain(self, segment_name: str, state_sequence: List[str]):
        """训练单个分段的马尔科夫链"""
        chain = self.markov_chains[segment_name]
        chain['states'] = set(state_sequence)
        
        # 构建转移矩阵
        for i in range(self.order, len(state_sequence)):
            if self.order == 1:
                current_state = state_sequence[i-1]
            else:
                current_state = tuple(state_sequence[i-self.order:i])
            
            next_state = state_sequence[i]
            
            chain['transition_matrix'][current_state][next_state] += 1
            chain['state_counts'][current_state] += 1
        
        # 计算转移概率
        for current_state, next_states in chain['transition_matrix'].items():
            total_transitions = sum(next_states.values())
            chain['transition_probabilities'][current_state] = {
                next_state: count / total_transitions 
                for next_state, count in next_states.items()
            }
    
    def train_all_segments(self, data: List[Dict]):
        """训练所有分段的马尔科夫链"""
        # print(f"训练分段马尔科夫链 (2+2+3格式)...")
        
        # 创建分段序列
        segment_sequences = self.create_segment_sequences(data)
        
        # 训练每个分段
        for segment_name, sequence in segment_sequences.items():
            # print(f"训练{segment_name}: {len(set(sequence))}个状态")
            self.train_segment_markov_chain(segment_name, sequence)
        
        # 分析每个分段
        self.analyze_segments()
    
    def predict_segment(self, segment_name: str, recent_states: List[str]) -> Tuple[str, float]:
        """预测单个分段的下一状态"""
        chain = self.markov_chains[segment_name]

        if len(recent_states) < self.order:
            # 返回最常见状态
            if chain['state_counts']:
                most_common = max(chain['state_counts'], key=chain['state_counts'].get)
                return most_common, 0.1
            else:
                return 'xx', 0.1  # 默认状态

        # 构建当前状态
        if self.order == 1:
            current_state = recent_states[-1]
        else:
            current_state = tuple(recent_states[-self.order:])

        # 查找转移概率
        if current_state in chain['transition_probabilities']:
            transitions = chain['transition_probabilities'][current_state]

            # 使用概率采样而不是总是选择最高概率的状态
            states = list(transitions.keys())
            probabilities = list(transitions.values())

            # 归一化概率
            total_prob = sum(probabilities)
            if total_prob > 0:
                probabilities = [p / total_prob for p in probabilities]
            else:
                probabilities = [1.0 / len(states)] * len(states)

            # 概率采样
            selected_state = np.random.choice(states, p=probabilities)
            confidence = transitions[selected_state]
            return selected_state, confidence
        else:
            # 回退到最常见状态
            if chain['state_counts']:
                # 也对回退情况使用概率采样
                states = list(chain['state_counts'].keys())
                counts = list(chain['state_counts'].values())
                total_count = sum(counts)

                if total_count > 0:
                    probabilities = [c / total_count for c in counts]
                    selected_state = np.random.choice(states, p=probabilities)
                    return selected_state, 0.05
                else:
                    most_common = max(chain['state_counts'], key=chain['state_counts'].get)
                    return most_common, 0.05
            else:
                return 'xx', 0.05
    
    def predict_target_zero_count(self, recent_data: List[Dict]) -> int:
        """改进的目标0分区数量预测 - 支持2-5个0分区，增加多样性"""
        if not recent_data:
            return 3  # 默认3个

        # 多时间窗口分析
        windows = [10, 30, 100]  # 短期、中期、长期
        weights = [0.5, 0.3, 0.2]  # 权重：短期影响最大

        weighted_avg = 0
        total_weight = 0

        for window, weight in zip(windows, weights):
            if len(recent_data) >= window:
                window_data = recent_data[-window:]
                window_counts = []
                for record in window_data:
                    zone_dist = self.parse_zone_ratio(record['分区比'])
                    zero_count = sum(1 for count in zone_dist if count == 0)
                    window_counts.append(zero_count)

                window_avg = np.mean(window_counts)
                weighted_avg += window_avg * weight
                total_weight += weight

        if total_weight > 0:
            final_avg = weighted_avg / total_weight
        else:
            final_avg = 3.13  # 历史期望值

        # 分析最近50期的0分区数量分布
        recent_50_data = recent_data[-50:] if len(recent_data) >= 50 else recent_data
        count_distribution = {2: 0, 3: 0, 4: 0, 5: 0}

        if recent_50_data:
            for record in recent_50_data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                zero_count = sum(1 for count in zone_dist if count == 0)
                if zero_count in count_distribution:
                    count_distribution[zero_count] += 1

            # 计算各数量的比例
            total_periods = len(recent_50_data)
            count_ratios = {k: v / total_periods for k, v in count_distribution.items()}
        else:
            count_ratios = {2: 0.1, 3: 0.5, 4: 0.35, 5: 0.05}  # 默认分布

        # 基于加权平均值确定基础倾向
        if final_avg <= 2.5:
            base_preference = 2
        elif final_avg <= 3.2:
            base_preference = 3
        elif final_avg <= 3.8:
            base_preference = 4
        else:
            base_preference = 5

        # 计算各选项的综合得分（降低历史趋势权重）
        scores = {}
        for count in [2, 3, 4, 5]:
            # 基础得分：基于加权平均值的距离
            distance_score = 1 / (1 + abs(final_avg - count))

            # 历史趋势得分：降低权重从30%到10%
            trend_score = count_ratios.get(count, 0)

            # 多样性奖励：避免过度集中
            diversity_bonus = 0.1 if count != base_preference else 0

            # 综合得分
            scores[count] = distance_score * 0.7 + trend_score * 0.1 + diversity_bonus * 0.2

        # 引入适度随机性：基于得分进行概率选择
        total_score = sum(scores.values())
        if total_score > 0:
            probabilities = {k: v / total_score for k, v in scores.items()}

            # 使用numpy随机选择
            counts = list(probabilities.keys())
            probs = list(probabilities.values())
            selected_count = np.random.choice(counts, p=probs)

            return selected_count
        else:
            return 3  # 默认返回3

    def adjust_pattern_to_target_count(self, pattern: str, target_count: int, segment_confidences: Dict) -> str:
        """调整模式以达到目标0分区数量 - 支持2-5个0分区"""
        current_count = pattern.count('0')

        # 确保目标数量在合理范围内
        target_count = max(2, min(5, target_count))

        if current_count == target_count:
            return pattern  # 已经符合目标

        pattern_list = list(pattern)

        if current_count < target_count:
            # 需要增加0分区
            needed = target_count - current_count

            # 找到置信度最低的'x'位置，转换为'0'
            x_positions = [i for i, char in enumerate(pattern_list) if char == 'x']

            # 根据分段置信度排序（置信度低的优先转换）
            position_scores = []
            for pos in x_positions:
                # 确定位置属于哪个分段
                if pos in [0, 1]:
                    segment = 'segment_1'
                elif pos in [2, 3]:
                    segment = 'segment_2'
                else:
                    segment = 'segment_3'

                # 置信度越低，越容易被转换
                score = 1 - segment_confidences.get(segment, 0.5)
                position_scores.append((pos, score))

            # 按分数排序，选择前needed个位置
            position_scores.sort(key=lambda x: x[1], reverse=True)
            for i in range(min(needed, len(position_scores))):
                pos = position_scores[i][0]
                pattern_list[pos] = '0'

        elif current_count > target_count:
            # 需要减少0分区
            excess = current_count - target_count

            # 找到置信度最低的'0'位置，转换为'x'
            zero_positions = [i for i, char in enumerate(pattern_list) if char == '0']

            # 根据分段置信度排序（置信度低的优先转换）
            position_scores = []
            for pos in zero_positions:
                # 确定位置属于哪个分段
                if pos in [0, 1]:
                    segment = 'segment_1'
                elif pos in [2, 3]:
                    segment = 'segment_2'
                else:
                    segment = 'segment_3'

                # 置信度越低，越容易被转换
                score = 1 - segment_confidences.get(segment, 0.5)
                position_scores.append((pos, score))

            # 按分数排序，选择前excess个位置
            position_scores.sort(key=lambda x: x[1], reverse=True)
            for i in range(min(excess, len(position_scores))):
                pos = position_scores[i][0]
                pattern_list[pos] = 'x'

        return ''.join(pattern_list)

    def predict_full_pattern(self, recent_data: List[Dict]) -> Tuple[str, Dict]:
        """预测完整的7位模式，确保0分区数量为3或4个"""
        if not recent_data:
            return "xx0x0x0", {}

        # 预测目标0分区数量
        target_zero_count = self.predict_target_zero_count(recent_data)

        # 为每个分段创建历史序列
        segment_sequences = self.create_segment_sequences(recent_data)

        # 预测每个分段
        segment_predictions = {}
        segment_confidences = {}

        for segment_name, positions in self.segments.items():
            sequence = segment_sequences[segment_name]
            prediction, confidence = self.predict_segment(segment_name, sequence)
            segment_predictions[segment_name] = prediction
            segment_confidences[segment_name] = confidence

        # 组合成完整模式
        full_pattern = ['x'] * 7

        for segment_name, positions in self.segments.items():
            segment_pattern = segment_predictions[segment_name]
            for i, pos in enumerate(positions):
                if i < len(segment_pattern):
                    full_pattern[pos] = segment_pattern[i]

        initial_pattern = ''.join(full_pattern)

        # 调整模式以达到目标数量
        adjusted_pattern = self.adjust_pattern_to_target_count(
            initial_pattern, target_zero_count, segment_confidences
        )

        # 计算整体置信度
        overall_confidence = np.mean(list(segment_confidences.values()))

        return adjusted_pattern, {
            'segment_predictions': segment_predictions,
            'segment_confidences': segment_confidences,
            'overall_confidence': overall_confidence,
            'initial_pattern': initial_pattern,
            'target_zero_count': target_zero_count,
            'final_zero_count': adjusted_pattern.count('0')
        }

    def get_recent_patterns_and_counts(self, recent_data: List[Dict], lookback_periods: int = 20) -> Tuple[set, set]:
        """获取最近N期内出现过的0位置模式和0分区数量"""
        recent_patterns = set()
        recent_zero_counts = set()

        # 取最近N期数据
        recent_subset = recent_data[-lookback_periods:] if len(recent_data) >= lookback_periods else recent_data

        for record in recent_subset:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            zero_count = pattern.count('0')

            recent_patterns.add(pattern)
            recent_zero_counts.add(zero_count)

        return recent_patterns, recent_zero_counts

    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])

    def predict_multiple_patterns(self, recent_data: List[Dict], num_predictions: int = 5, exclude_recent: bool = True) -> List[Tuple[str, float]]:
        """生成多组预测模式，可选择排除最近20期内出现过的模式"""
        if not recent_data:
            return [("xx0x0x0", 0.1)] * num_predictions

        # 获取最近20期内出现过的模式和数量
        recent_patterns, recent_zero_counts = self.get_recent_patterns_and_counts(recent_data, 20) if exclude_recent else (set(), set())

        predictions = []
        max_attempts = num_predictions * 10  # 最大尝试次数，避免无限循环
        attempts = 0

        # 为每个分段创建历史序列
        segment_sequences = self.create_segment_sequences(recent_data)

        while len(predictions) < num_predictions and attempts < max_attempts:
            attempts += 1

            # 预测目标0分区数量
            base_target_zero_count = self.predict_target_zero_count(recent_data)

            # 如果启用排除功能，避免使用最近20期内出现过的0分区数量
            if exclude_recent and base_target_zero_count in recent_zero_counts:
                # 尝试使用其他数量（3或4）
                alternative_counts = [3, 4]
                alternative_counts = [c for c in alternative_counts if c not in recent_zero_counts]
                if alternative_counts:
                    target_zero_count = alternative_counts[0]
                else:
                    target_zero_count = base_target_zero_count  # 如果都被排除，使用原始预测
            else:
                target_zero_count = base_target_zero_count

            # 预测每个分段，添加一些随机性
            segment_predictions = {}
            segment_confidences = {}

            for segment_name, positions in self.segments.items():
                sequence = segment_sequences[segment_name]
                chain = self.markov_chains[segment_name]

                if len(sequence) < self.order:
                    # 返回最常见状态
                    if chain['state_counts']:
                        most_common = max(chain['state_counts'], key=chain['state_counts'].get)
                        segment_predictions[segment_name] = most_common
                        segment_confidences[segment_name] = 0.1
                    else:
                        segment_predictions[segment_name] = 'xx'
                        segment_confidences[segment_name] = 0.1
                    continue

                # 构建当前状态
                if self.order == 1:
                    current_state = sequence[-1]
                else:
                    current_state = tuple(sequence[-self.order:])

                # 查找转移概率
                if current_state in chain['transition_probabilities']:
                    transitions = chain['transition_probabilities'][current_state]

                    # 对于第一个预测，选择最高概率
                    if len(predictions) == 0:
                        best_next_state = max(transitions, key=transitions.get)
                        confidence = transitions[best_next_state]
                    else:
                        # 对于其他预测，基于概率分布随机选择
                        states = list(transitions.keys())
                        probs = list(transitions.values())

                        # 添加一些随机性，选择概率较高的状态
                        if len(states) > 1:
                            # 按概率排序，选择前几个
                            sorted_items = sorted(zip(states, probs), key=lambda x: x[1], reverse=True)
                            top_states = sorted_items[:min(3, len(sorted_items))]

                            # 从前几个中选择
                            if len(predictions) < len(top_states):
                                best_next_state = top_states[len(predictions) % len(top_states)][0]
                                confidence = top_states[len(predictions) % len(top_states)][1]
                            else:
                                best_next_state = top_states[0][0]
                                confidence = top_states[0][1]
                        else:
                            best_next_state = states[0]
                            confidence = probs[0]

                    segment_predictions[segment_name] = best_next_state
                    segment_confidences[segment_name] = confidence
                else:
                    # 回退到最常见状态
                    if chain['state_counts']:
                        most_common = max(chain['state_counts'], key=chain['state_counts'].get)
                        segment_predictions[segment_name] = most_common
                        segment_confidences[segment_name] = 0.05
                    else:
                        segment_predictions[segment_name] = 'xx'
                        segment_confidences[segment_name] = 0.05

            # 组合成完整模式
            full_pattern = ['x'] * 7

            for segment_name, positions in self.segments.items():
                segment_pattern = segment_predictions[segment_name]
                for j, pos in enumerate(positions):
                    if j < len(segment_pattern):
                        full_pattern[pos] = segment_pattern[j]

            initial_pattern = ''.join(full_pattern)

            # 调整模式以达到目标数量
            adjusted_pattern = self.adjust_pattern_to_target_count(
                initial_pattern, target_zero_count, segment_confidences
            )

            # 如果启用排除功能，检查是否与最近20期内的模式重复
            if exclude_recent and adjusted_pattern in recent_patterns:
                continue  # 跳过重复的模式，尝试生成新的

            # 计算整体置信度
            overall_confidence = np.mean(list(segment_confidences.values()))

            predictions.append((adjusted_pattern, overall_confidence))

        # 如果无法生成足够的非重复预测，用原始方法补充
        while len(predictions) < num_predictions:
            target_zero_count = self.predict_target_zero_count(recent_data)
            segment_predictions = {}
            segment_confidences = {}

            for segment_name, positions in self.segments.items():
                sequence = segment_sequences[segment_name]
                chain = self.markov_chains[segment_name]

                if chain['state_counts']:
                    most_common = max(chain['state_counts'], key=chain['state_counts'].get)
                    segment_predictions[segment_name] = most_common
                    segment_confidences[segment_name] = 0.1
                else:
                    segment_predictions[segment_name] = 'xx'
                    segment_confidences[segment_name] = 0.1

            full_pattern = ['x'] * 7
            for segment_name, positions in self.segments.items():
                segment_pattern = segment_predictions[segment_name]
                for j, pos in enumerate(positions):
                    if j < len(segment_pattern):
                        full_pattern[pos] = segment_pattern[j]

            initial_pattern = ''.join(full_pattern)
            adjusted_pattern = self.adjust_pattern_to_target_count(
                initial_pattern, target_zero_count, segment_confidences
            )
            overall_confidence = np.mean(list(segment_confidences.values()))

            predictions.append((adjusted_pattern, overall_confidence))

        return predictions
    
    def analyze_segments(self):
        """分析各分段的特征"""
        # print(f"\n分段马尔科夫链分析:")
        
        for segment_name, positions in self.segments.items():
            chain = self.markov_chains[segment_name]
            
            # print(f"\n{segment_name} (分区{[p+1 for p in positions]}):")
            # print(f"  状态数: {len(chain['states'])}")
            # print(f"  转移数: {len(chain['transition_matrix'])}")
            
            # 最常见状态
            if chain['state_counts']:
                most_common_states = chain['state_counts'].most_common(3)
                # print(f"  最常见状态:")
                for state, count in most_common_states:
                    pct = count / sum(chain['state_counts'].values()) * 100
                    # print(f"    {state}: {count}次 ({pct:.1f}%)")
            
            # 最常见转移
            if chain['transition_matrix']:
                all_transitions = []
                for current, next_states in chain['transition_matrix'].items():
                    for next_state, count in next_states.items():
                        all_transitions.append((current, next_state, count))
                
                all_transitions.sort(key=lambda x: x[2], reverse=True)
                # print(f"  最常见转移:")
                for current, next_state, count in all_transitions[:3]:
                    # print(f"    {current} → {next_state}: {count}次")
                    pass


class ComparisonTester:
    """对比测试器"""
    
    def __init__(self):
        self.segmented_markov = SegmentedMarkovChain(order=1)
        
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def run_comparison_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = None):
        """运行对比测试"""
        print("=" * 80)
        print("分段马尔科夫链 (2+2+3) 测试")
        print("=" * 80)

        # 确定训练样本大小
        if train_periods is None:
            # 使用除测试期外的所有数据
            train_data = data[:-test_periods]
            train_size = len(train_data)
        else:
            # 使用指定数量的训练期数
            train_size = min(train_periods, len(data) - test_periods)
            train_data = data[-(test_periods + train_size):-test_periods]

        test_data = data[-test_periods:]

        print(f"训练样本: {train_size}期 (第{data[-(test_periods + train_size)]['期号']}期 - 第{train_data[-1]['期号']}期)")
        print(f"测试样本: {test_periods}期 (第{test_data[0]['期号']}期 - 第{test_data[-1]['期号']}期)")

        # 训练分段马尔科夫链
        self.segmented_markov.train_all_segments(train_data)
        
        # 预测测试
        print(f"\n预测结果 (最近{test_periods}期):")
        print("期号      实际模式    分段预测    实际0数 预测0数 匹配度  置信度")
        print("-" * 70)
        
        total_matches = 0
        total_confidence = 0
        segment_matches = {'segment_1': 0, 'segment_2': 0, 'segment_3': 0}
        zero_count_matches = 0
        
        for i, test_record in enumerate(test_data):
            # 获取历史数据
            history = train_data + test_data[:i]
            recent_history = history[-10:]  # 最近10期
            
            # 分段预测
            predicted_pattern, prediction_info = self.segmented_markov.predict_full_pattern(recent_history)
            
            # 实际结果
            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)
            
            # 计算匹配度
            total_match = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
            total_matches += total_match
            total_confidence += prediction_info['overall_confidence']

            # 检查0分区数量匹配
            actual_zero_count = actual_pattern.count('0')
            predicted_zero_count = prediction_info['final_zero_count']
            if actual_zero_count == predicted_zero_count:
                zero_count_matches += 1
            
            # 分段匹配度
            seg1_actual = actual_pattern[0:2]
            seg2_actual = actual_pattern[2:4]
            seg3_actual = actual_pattern[4:7]
            
            seg1_pred = prediction_info['segment_predictions']['segment_1']
            seg2_pred = prediction_info['segment_predictions']['segment_2']
            seg3_pred = prediction_info['segment_predictions']['segment_3']
            
            seg1_match = sum(1 for p, a in zip(seg1_pred, seg1_actual) if p == a)
            seg2_match = sum(1 for p, a in zip(seg2_pred, seg2_actual) if p == a)
            seg3_match = sum(1 for p, a in zip(seg3_pred, seg3_actual) if p == a)
            
            segment_matches['segment_1'] += seg1_match
            segment_matches['segment_2'] += seg2_match
            segment_matches['segment_3'] += seg3_match

            print(f"{test_record['期号']:<8} {actual_pattern:<10} {predicted_pattern:<10} "
                  f"{actual_zero_count:<6} {predicted_zero_count:<6} "
                  f"{total_match}/7      {prediction_info['overall_confidence']:.2f}")
        
        # 统计结果
        print(f"\n" + "=" * 80)
        print("分段马尔科夫链测试结果")
        print("=" * 80)
        
        avg_total_accuracy = total_matches / (test_periods * 7)
        avg_confidence = total_confidence / test_periods
        zero_count_accuracy = zero_count_matches / test_periods

        print(f"整体性能:")
        print(f"  平均位置准确率: {avg_total_accuracy:.1%}")
        print(f"  0分区数量准确率: {zero_count_accuracy:.1%}")
        print(f"  平均置信度: {avg_confidence:.2f}")
        
        # 计算分段性能但不显示
        seg1_accuracy = segment_matches['segment_1'] / (test_periods * 2)
        seg2_accuracy = segment_matches['segment_2'] / (test_periods * 2)
        seg3_accuracy = segment_matches['segment_3'] / (test_periods * 3)
        
        print(f"\n分段马尔科夫链优势:")
        print(f"1. 状态空间减少:")
        print(f"   - 分段1: 2^2 = 4种状态")
        print(f"   - 分段2: 2^2 = 4种状态")
        print(f"   - 分段3: 2^3 = 8种状态")
        print(f"   - 总计: 16种状态 vs 原来128种")
        
        print(f"\n2. 数据充分性提升:")
        print(f"   - 每个状态有更多训练样本")
        print(f"   - 转移概率更可靠")
        print(f"   - 减少数据稀疏性问题")
        
        print(f"\n数量控制效果:")
        print(f"  所有预测都控制在3或4个0分区")
        print(f"  数量准确率: {zero_count_accuracy:.1%}")
        print(f"  符合历史分布: 3个(52.5%) + 4个(27.8%) = 80.3%")

        return {
            'total_accuracy': avg_total_accuracy,
            'zero_count_accuracy': zero_count_accuracy,
            'segment_accuracies': {
                'segment_1': seg1_accuracy,
                'segment_2': seg2_accuracy,
                'segment_3': seg3_accuracy
            },
            'confidence': avg_confidence,
            'train_size': train_size
        }

    def run_markov_prediction_test(self, data: List[Dict], test_periods: int = 20, train_periods: int = 200):
        """运行马尔科夫链5组预测测试"""
        print("=" * 80)
        print("马尔科夫链5组预测测试")
        print("=" * 80)

        # 确定训练样本大小
        train_data = data[-(test_periods + train_periods):-test_periods]
        test_data = data[-test_periods:]

        print(f"训练样本: {train_periods}期 (第{train_data[0]['期号']}期 - 第{train_data[-1]['期号']}期)")
        print(f"测试样本: {test_periods}期 (第{test_data[0]['期号']}期 - 第{test_data[-1]['期号']}期)")
        print(f"排除功能: 启用 (排除最近50期内出现过的0位置组合和数量)")

        # 训练分段马尔科夫链
        self.segmented_markov.train_all_segments(train_data)

        print(f"\n预测结果 (最近{test_periods}期):")
        print("-" * 80)

        for i, test_record in enumerate(test_data):
            # 获取历史数据
            history = train_data + test_data[:i]
            recent_history = history[-10:]  # 最近10期

            # 获取实际模式
            actual_zone_dist = self.parse_zone_ratio(test_record['分区比'])
            actual_pattern = self.get_zero_pattern(actual_zone_dist)

            print(f"期号{test_record['期号']}")
            print(f"实际模式：{actual_pattern}")

            # 生成5组预测（启用排除最近20期功能）
            predictions = self.segmented_markov.predict_multiple_patterns(recent_history, num_predictions=5, exclude_recent=True)

            for j, (predicted_pattern, confidence) in enumerate(predictions):
                # 计算匹配度
                match_count = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
                match_ratio = f"{match_count}/7"

                print(f"马尔科夫：{predicted_pattern}      匹配度：{match_ratio}")

            print()  # 空行分隔

        return test_data


def main():
    """主函数"""
    try:
        # 加载数据
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')

        if len(data) < 100:
            print("数据不足")
            return

        # 运行马尔科夫链5组预测测试
        tester = ComparisonTester()
        test_data = tester.run_markov_prediction_test(data, test_periods=20, train_periods=200)

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
